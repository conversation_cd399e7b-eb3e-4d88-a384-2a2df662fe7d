import { redirect, fail } from '@sveltejs/kit';
import { eq, and, desc, count, inArray } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { saveFile, getAbsoluteFilePath } from '$lib/server/storage/fileStorage';
import { extractArchive, deleteDirectory } from '$lib/server/storage/archiveExtractor';
import { analyzePdfContent } from '$lib/server/api/claude-api';
import crypto from 'crypto';
import path from 'path';
import fs from 'fs';
import pdfParse from 'pdf-parse';
import type { ExtractedTable, ExtractedPage } from '$lib/types';
import type { Actions, PageServerLoad } from './$types';

async function analyzePdfAndSaveResults(submissionId: string, filePath: string) {
  try {
    console.log(`Analyzing PDF for submission ${submissionId}`);

    const absoluteFilePath = getAbsoluteFilePath(filePath);
    if (!fs.existsSync(absoluteFilePath)) {
      console.error(`PDF file not found: ${absoluteFilePath}`);
      return false;
    }

    const pdfBuffer = fs.readFileSync(absoluteFilePath);
    const pdfData = await pdfParse(pdfBuffer);

    const { extractTextFromPDF, filterPages } = await import('$lib/server/pdf/pdfAnalysis');
    const extractedPages = await extractTextFromPDF(absoluteFilePath);
    const processedPages = filterPages(extractedPages);
    const includedPages = processedPages.filter(page => !page.isExcluded);
    const includedPageNumbers = includedPages.map(page => page.pageNumber);
    console.log(`Sending ${includedPageNumbers.length} filtered pages to Claude API: ${includedPageNumbers.join(', ')}`);

    const analysisResult = await analyzePdfContent(pdfBuffer, pdfData.numpages, includedPageNumbers);

    if (!analysisResult.success) {
      console.error(`Analysis failed: ${analysisResult.error}`);
      return false;
    }

    if (!analysisResult.tables || analysisResult.tables.length === 0) {
      console.error('No tables found in the PDF');
      return false;
    }

    const analysisId = crypto.randomUUID();
    await db.insert(table.complexityAnalysis).values({
      id: analysisId,
      submissionId: submissionId,
      tables: JSON.stringify(analysisResult.tables || []),
      includedPages: JSON.stringify(analysisResult.includedPages || []),
      excludedPages: JSON.stringify(analysisResult.excludedPages || [])
    });
    await db.update(table.submission)
      .set({ hasComplexityAnalysis: true })
      .where(eq(table.submission.id, submissionId));

    console.log(`Analysis completed and saved for submission ${submissionId}`);
    return true;
  } catch (error) {
    console.error('Error analyzing PDF:', error);
    return false;
  }
}

export const load: PageServerLoad = async (event) => {
  const { user } = event.locals;
  const { projectId } = event.params;

  if (!user) {
    throw redirect(302, '/login');
  }

  if (user.role !== 'student') {
    throw redirect(302, '/dashboard');
  }

  const [project] = await db
    .select({
      id: table.project.id,
      name: table.project.name,
      description: table.project.description,
      maxAttempts: table.project.maxAttempts,
      deadline: table.project.deadline,
      isHidden: table.project.isHidden,
      createdBy: table.project.createdBy,
      createdAt: table.project.createdAt
    })
    .from(table.project)
    .where(eq(table.project.id, projectId));

  if (!project) {
    throw redirect(302, '/dashboard/student');
  }

  if (project.isHidden) {
    throw redirect(302, '/dashboard/student');
  }

  const [projectStudent] = await db
    .select()
    .from(table.projectStudent)
    .where(
      and(
        eq(table.projectStudent.projectId, projectId),
        eq(table.projectStudent.studentId, user.id)
      )
    );

  let isAssigned = !!projectStudent;

  if (!isAssigned) {
    const studentGroups = await db
      .select({
        groupId: table.groupMember.groupId
      })
      .from(table.groupMember)
      .where(eq(table.groupMember.studentId, user.id));

    const studentGroupIds = studentGroups.map(g => g.groupId);

    if (studentGroupIds.length > 0) {
      // Check if any group associated with this project
      const [projectGroup] = await db
        .select()
        .from(table.projectGroup)
        .where(
          and(
            eq(table.projectGroup.projectId, projectId),
            inArray(table.projectGroup.groupId, studentGroupIds)
          )
        );

      if (projectGroup) {
        // Check if this student is assigned to the project
        const [assignedStudent] = await db
          .select({
            studentId: table.projectStudent.studentId
          })
          .from(table.projectStudent)
          .where(
            and(
              eq(table.projectStudent.projectId, projectId),
              eq(table.projectStudent.studentId, user.id)
            )
          );

        isAssigned = !!assignedStudent;
      }
    }
  }

  if (!isAssigned) {
    throw redirect(302, '/dashboard/student');
  }

  const [lecturer] = await db
    .select({
      id: table.user.id,
      username: table.user.username
    })
    .from(table.user)
    .where(eq(table.user.id, project.createdBy));

  const submissions = await db
    .select({
      id: table.submission.id,
      filePath: table.submission.filePath,
      fileSize: table.submission.fileSize,
      originalFilename: table.submission.originalFilename,
      submittedAt: table.submission.submittedAt
    })
    .from(table.submission)
    .where(
      and(
        eq(table.submission.projectId, projectId),
        eq(table.submission.studentId, user.id)
      )
    )
    .orderBy(desc(table.submission.submittedAt))
    .limit(5);

  const [attemptCount] = await db
    .select({ count: count() })
    .from(table.submissionAttempt)
    .where(
      and(
        eq(table.submissionAttempt.projectId, projectId),
        eq(table.submissionAttempt.studentId, user.id)
      )
    );

  return {
    user,
    project,
    lecturer,
    submissions,
    attemptCount: attemptCount.count
  };
};

export const actions: Actions = {
  submitFile: async (event) => {
    const { user } = event.locals;
    const { projectId } = event.params;

    if (!user || user.role !== 'student') {
      return fail(403, { message: 'Unauthorized' });
    }

    try {
      const formData = await event.request.formData();
      const file = formData.get('file') as File;

      if (!file || file.size === 0) {
        return fail(400, { message: 'Please select a file to upload' });
      }

      const [project] = await db
        .select()
        .from(table.project)
        .where(eq(table.project.id, projectId));

      if (!project) {
        return fail(404, { message: 'Project not found' });
      }

      // Check if deadline
      if (project.deadline && new Date() > new Date(project.deadline)) {
        return fail(400, { message: `Submission deadline has passed (${new Date(project.deadline).toLocaleString()})` });
      }

      // Check if max attempts
      if (project.maxAttempts && project.maxAttempts > 0) {
        const [attemptCount] = await db
          .select({ count: count() })
          .from(table.submissionAttempt)
          .where(
            and(
              eq(table.submissionAttempt.projectId, projectId),
              eq(table.submissionAttempt.studentId, user.id)
            )
          );

        if (attemptCount.count >= project.maxAttempts) {
          return fail(400, { message: `Maximum number of submission attempts (${project.maxAttempts}) has been reached` });
        }
      }

      const [projectStudent] = await db
        .select()
        .from(table.projectStudent)
        .where(
          and(
            eq(table.projectStudent.projectId, projectId),
            eq(table.projectStudent.studentId, user.id)
          )
        );

      if (!projectStudent) {
        const studentGroups = await db
          .select({
            groupId: table.groupMember.groupId
          })
          .from(table.groupMember)
          .where(eq(table.groupMember.studentId, user.id));

        const studentGroupIds = studentGroups.map(g => g.groupId);

        if (studentGroupIds.length === 0) {
          return fail(403, { message: 'You are not assigned to this project' });
        }

        const [projectGroup] = await db
          .select()
          .from(table.projectGroup)
          .where(
            and(
              eq(table.projectGroup.projectId, projectId),
              eq(table.projectGroup.groupId, studentGroupIds[0])
            )
          );

        if (!projectGroup) {
          return fail(403, { message: 'You are not assigned to this project' });
        }

        const groupStudents = await db
          .select({
            studentId: table.groupMember.studentId
          })
          .from(table.groupMember)
          .where(eq(table.groupMember.groupId, projectGroup.groupId));

        const assignedStudents = await db
          .select({
            studentId: table.projectStudent.studentId
          })
          .from(table.projectStudent)
          .where(eq(table.projectStudent.projectId, projectId));

        const groupStudentIds = new Set(groupStudents.map(s => s.studentId));
        const assignedStudentIds = new Set(assignedStudents.map(s => s.studentId));

        const allStudentsAssigned = [...groupStudentIds].every(id => assignedStudentIds.has(id));

        if (!allStudentsAssigned) {
          return fail(403, { message: 'You are not assigned to this project' });
        }
      }

      const fileExt = path.extname(file.name).toLowerCase();
      const isArchive = ['.zip', '.rar', '.7z'].includes(fileExt);

      if (isArchive) {
        console.log(`Processing archive file: ${file.name}`);

        let extractedFiles;
        let tempFilePath = '';

        try {
          // Convert the file to a buffer
          const arrayBuffer = await file.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);

          // Create a temporary file to extract from
          const tempDir = path.join(process.cwd(), 'temp');
          if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
          }

          tempFilePath = path.join(tempDir, `${crypto.randomUUID()}${fileExt}`);
          fs.writeFileSync(tempFilePath, buffer);

          extractedFiles = await extractArchive(tempFilePath, user.id);

          try {
            fs.unlinkSync(tempFilePath);
          } catch (cleanupError) {
            console.error('Error cleaning up temporary file:', cleanupError);
          }

          if (!extractedFiles || !extractedFiles.pdfFile) {
            if (extractedFiles && extractedFiles.extractDir) {
              try {
                await deleteDirectory(extractedFiles.extractDir);
              } catch (cleanupError) {
                console.error('Error cleaning up extraction directory:', cleanupError);
              }
            }
            return fail(400, { message: 'No PDF file found in the archive. Please include at least one PDF file.' });
          }

          const previousSubmissions = await db
            .select({
              id: table.submission.id,
              filePath: table.submission.filePath
            })
            .from(table.submission)
            .where(
              and(
                eq(table.submission.projectId, projectId),
                eq(table.submission.studentId, user.id)
              )
            );
          // If other submission exists, delete current
          if (previousSubmissions.length > 0) {
            for (const submission of previousSubmissions) {
              // First delete CS files
              await db
                .delete(table.csFile)
                .where(eq(table.csFile.submissionId, submission.id));

              // Delete complexity analysis if exists
              await db
                .delete(table.complexityAnalysis)
                .where(eq(table.complexityAnalysis.submissionId, submission.id));

              // Delete submission attempts
              await db
                .delete(table.submissionAttempt)
                .where(eq(table.submissionAttempt.submissionId, submission.id));

              try {
                const absolutePath = getAbsoluteFilePath(submission.filePath);
                if (fs.existsSync(absolutePath)) {
                  fs.unlinkSync(absolutePath);
                  console.log(`Deleted previous PDF file: ${absolutePath}`);
                }
              } catch (error) {
                console.error(`Error deleting previous PDF file: ${error}`);
              }
            }

            await db
              .delete(table.submission)
              .where(
                and(
                  eq(table.submission.projectId, projectId),
                  eq(table.submission.studentId, user.id)
                )
              );

            console.log(`Deleted ${previousSubmissions.length} previous submissions`);
          }

          const pdfBuffer = fs.readFileSync(extractedFiles.pdfFile.path);
          const permanentFilePath = await saveFile(user.id, pdfBuffer, extractedFiles.pdfFile.name);
          console.log(`Saved PDF to permanent location: ${permanentFilePath}`);

          const submissionId = crypto.randomUUID();
          await db.insert(table.submission).values({
            id: submissionId,
            projectId,
            studentId: user.id,
            filePath: permanentFilePath,
            fileSize: extractedFiles.pdfFile.size,
            originalFilename: extractedFiles.pdfFile.name,
            hasComplexityAnalysis: false
          });

          await db.insert(table.submissionAttempt).values({
            id: crypto.randomUUID(),
            projectId,
            studentId: user.id,
            submissionId,
            attemptedAt: new Date()
          });

          if (extractedFiles.csFiles.length > 0) {
            console.log(`Found ${extractedFiles.csFiles.length} CS files in the archive`);

            const validCsFiles = extractedFiles.csFiles.filter(file => {
              if (file.path.includes('__MACOSX') || file.name.startsWith('._')) {
                console.log(`Skipping macOS system file: ${file.path}`);
                return false;
              }

              try {
                Buffer.from(file.content).toString('utf8');
                return true;
              } catch (error) {
                console.error(`Invalid UTF-8 encoding: ${file.path}`);
                return false;
              }
            });

            console.log(`Processing ${validCsFiles.length} valid CS files after filtering`);

            for (const csFile of validCsFiles) {
              try {
                const sanitizedContent = csFile.content
                  .replace(/\0/g, '')
                  .replace(/[^\x20-\x7E\x0A\x0D\xA0-\xFF]/g, '');

                await db.insert(table.csFile).values({
                  id: crypto.randomUUID(),
                  submissionId,
                  fileName: csFile.name,
                  filePath: csFile.path,
                  fileContent: sanitizedContent
                });
              } catch (error) {
                console.error(`Error inserting CS file ${csFile.name}:`, error);
              }
            }
          }

          try {
            await deleteDirectory(extractedFiles.extractDir);
            console.log(`Cleaned up extraction directory: ${extractedFiles.extractDir}`);
          } catch (cleanupError) {
            console.error('Error cleaning up extraction directory:', cleanupError);
          }

          const analysisSuccess = await analyzePdfAndSaveResults(submissionId, permanentFilePath);

          return {
            success: true,
            message: `Your submission has been received successfully. ${extractedFiles.csFiles.length > 0 ? `${extractedFiles.csFiles.length} CS files were extracted.` : ''}`,
            submitted: true,
            submissionId,
            analysisComplete: analysisSuccess
          };
        } catch (error) {
          console.error('Error processing archive:', error);

          if (tempFilePath && fs.existsSync(tempFilePath)) {
            try {
              fs.unlinkSync(tempFilePath);
              console.log(`Cleaned up temporary file after error: ${tempFilePath}`);
            } catch (cleanupError) {
              console.error('Error cleaning up temporary file after error:', cleanupError);
            }
          }

          if (extractedFiles && extractedFiles.extractDir) {
            try {
              await deleteDirectory(extractedFiles.extractDir);
              console.log(`Cleaned up extraction directory after error: ${extractedFiles.extractDir}`);
            } catch (cleanupError) {
              console.error('Error cleaning up extraction directory after error:', cleanupError);
            }
          }

          let errorMessage = 'Error processing archive';

          const errorObj = error as Error;
          if (errorObj && errorObj.message) {
            if (errorObj.message.includes('7-Zip is not installed')) {
              errorMessage = 'This type of archive (.rar or .7z) requires 7-Zip to be installed on the server. Please use a .zip file instead.';
            } else if (errorObj.message.includes('No PDF file found')) {
              errorMessage = 'No PDF file found in the archive. Please include at least one PDF file.';
            } else {
              errorMessage = `Error processing archive: ${errorObj.message}`;
            }
          }

          return fail(500, { message: errorMessage });
        }
      } else {
        // Regular PDF file upload
        const previousSubmissions = await db
          .select({
            id: table.submission.id,
            filePath: table.submission.filePath
          })
          .from(table.submission)
          .where(
            and(
              eq(table.submission.projectId, projectId),
              eq(table.submission.studentId, user.id)
            )
          );

        // Delete previous
        if (previousSubmissions.length > 0) {
          for (const submission of previousSubmissions) {
            await db
              .delete(table.csFile)
              .where(eq(table.csFile.submissionId, submission.id));

            await db
              .delete(table.complexityAnalysis)
              .where(eq(table.complexityAnalysis.submissionId, submission.id));

            await db
              .delete(table.submissionAttempt)
              .where(eq(table.submissionAttempt.submissionId, submission.id));

            try {
              const absolutePath = getAbsoluteFilePath(submission.filePath);
              if (fs.existsSync(absolutePath)) {
                fs.unlinkSync(absolutePath);
                console.log(`Deleted previous PDF file: ${absolutePath}`);
              }
            } catch (error) {
              console.error(`Error deleting previous PDF file: ${error}`);
            }
          }
          await db
            .delete(table.submission)
            .where(
              and(
                eq(table.submission.projectId, projectId),
                eq(table.submission.studentId, user.id)
              )
            );

          console.log(`Deleted ${previousSubmissions.length} previous submissions`);
        }

        const filePath = await saveFile(user.id, file, file.name);

        const submissionId = crypto.randomUUID();
        await db.insert(table.submission).values({
          id: submissionId,
          projectId,
          studentId: user.id,
          filePath,
          fileSize: file.size,
          originalFilename: file.name,
          hasComplexityAnalysis: false
        });

        await db.insert(table.submissionAttempt).values({
          id: crypto.randomUUID(),
          projectId,
          studentId: user.id,
          submissionId,
          attemptedAt: new Date()
        });

        const analysisSuccess = await analyzePdfAndSaveResults(submissionId, filePath);

        return {
          success: true,
          message: 'Your submission has been received successfully',
          submitted: true,
          submissionId,
          analysisComplete: analysisSuccess
        };
      }
    } catch (error: any) {
      console.error('Submission error:', error);
      const errorMessage = error.message ? `${error.message}` : 'Unknown error';
      return fail(500, { message: `An error occurred during file upload: ${errorMessage}. Please try again.` });
    }
  }
};
