"filename", "language", "TypeScript", "SCSS", "HTML", "PostCSS", "Svelte", "comment", "blank", "total"
"c:\pdf-bak\src\app.css", "PostCSS", 0, 0, 0, 764, 0, 37, 97, 898
"c:\pdf-bak\src\app.d.ts", "TypeScript", 19, 0, 0, 0, 0, 2, 5, 26
"c:\pdf-bak\src\app.html", "HTML", 0, 0, 30, 0, 0, 0, 2, 32
"c:\pdf-bak\src\hooks.server.ts", "TypeScript", 63, 0, 0, 0, 0, 13, 15, 91
"c:\pdf-bak\src\lib\actions\clickOutside.ts", "TypeScript", 14, 0, 0, 0, 0, 0, 4, 18
"c:\pdf-bak\src\lib\components\ui\button\Button.svelte", "Svelte", 0, 0, 0, 0, 57, 0, 4, 61
"c:\pdf-bak\src\lib\components\ui\data\DataTable.svelte", "Svelte", 0, 0, 0, 0, 38, 0, 2, 40
"c:\pdf-bak\src\lib\components\ui\data\DetailItem.svelte", "Svelte", 0, 0, 0, 0, 10, 0, 2, 12
"c:\pdf-bak\src\lib\components\ui\data\KeyValueList.svelte", "Svelte", 0, 0, 0, 0, 20, 0, 2, 22
"c:\pdf-bak\src\lib\components\ui\data\ListItem.svelte", "Svelte", 0, 0, 0, 0, 12, 0, 2, 14
"c:\pdf-bak\src\lib\components\ui\data\StatCard.svelte", "Svelte", 0, 0, 0, 0, 27, 0, 3, 30
"c:\pdf-bak\src\lib\components\ui\data\StatLink.svelte", "Svelte", 0, 0, 0, 0, 41, 0, 2, 43
"c:\pdf-bak\src\lib\components\ui\data\StatTile.svelte", "Svelte", 0, 0, 0, 0, 87, 0, 6, 93
"c:\pdf-bak\src\lib\components\ui\feedback\AlertMessage.svelte", "Svelte", 0, 0, 0, 0, 91, 0, 11, 102
"c:\pdf-bak\src\lib\components\ui\feedback\Countdown.svelte", "Svelte", 0, 0, 0, 0, 85, 0, 23, 108
"c:\pdf-bak\src\lib\components\ui\feedback\EmptyState.svelte", "Svelte", 0, 0, 0, 0, 40, 0, 5, 45
"c:\pdf-bak\src\lib\components\ui\feedback\LoadingIndicator.svelte", "Svelte", 0, 0, 0, 0, 49, 0, 6, 55
"c:\pdf-bak\src\lib\components\ui\feedback\StatusBadge.svelte", "Svelte", 0, 0, 0, 0, 22, 0, 6, 28
"c:\pdf-bak\src\lib\components\ui\index.ts", "TypeScript", 34, 0, 0, 0, 0, 0, 8, 42
"c:\pdf-bak\src\lib\components\ui\input\FormInput.svelte", "Svelte", 0, 0, 0, 0, 82, 0, 5, 87
"c:\pdf-bak\src\lib\components\ui\input\FormSelect.svelte", "Svelte", 0, 0, 0, 0, 71, 0, 6, 77
"c:\pdf-bak\src\lib\components\ui\input\ManualTableInput.svelte", "Svelte", 0, 0, 0, 0, 191, 0, 18, 209
"c:\pdf-bak\src\lib\components\ui\input\PasswordStrengthMeter.svelte", "Svelte", 0, 0, 0, 0, 175, 0, 23, 198
"c:\pdf-bak\src\lib\components\ui\layout\AuthLayout.svelte", "Svelte", 0, 0, 0, 0, 44, 0, 6, 50
"c:\pdf-bak\src\lib\components\ui\layout\Card.svelte", "Svelte", 0, 0, 0, 0, 42, 0, 5, 47
"c:\pdf-bak\src\lib\components\ui\layout\DashboardSection.svelte", "Svelte", 0, 0, 0, 0, 42, 0, 5, 47
"c:\pdf-bak\src\lib\components\ui\layout\FormLayout.svelte", "Svelte", 0, 0, 0, 0, 83, 0, 10, 93
"c:\pdf-bak\src\lib\components\ui\layout\Header.svelte", "Svelte", 0, 0, 0, 0, 382, 5, 31, 418
"c:\pdf-bak\src\lib\components\ui\layout\PageHeader.svelte", "Svelte", 0, 0, 0, 0, 35, 0, 3, 38
"c:\pdf-bak\src\lib\components\ui\navigation\ActionButton.svelte", "Svelte", 0, 0, 0, 0, 50, 0, 4, 54
"c:\pdf-bak\src\lib\components\ui\navigation\BackButton.svelte", "Svelte", 0, 0, 0, 0, 30, 0, 6, 36
"c:\pdf-bak\src\lib\components\ui\navigation\NavLink.svelte", "Svelte", 0, 0, 0, 0, 71, 0, 8, 79
"c:\pdf-bak\src\lib\components\ui\navigation\ViewAllLink.svelte", "Svelte", 0, 0, 0, 0, 19, 0, 3, 22
"c:\pdf-bak\src\lib\components\ui\overlay\BatchProcessModal.svelte", "Svelte", 0, 0, 0, 0, 213, 0, 26, 239
"c:\pdf-bak\src\lib\components\ui\overlay\ManualTableEntryModal.svelte", "Svelte", 0, 0, 0, 0, 157, 0, 22, 179
"c:\pdf-bak\src\lib\components\ui\overlay\Modal.svelte", "Svelte", 0, 0, 0, 0, 82, 0, 13, 95
"c:\pdf-bak\src\lib\components\ui\overlay\PDFViewerModal.svelte", "Svelte", 0, 0, 0, 0, 212, 2, 24, 238
"c:\pdf-bak\src\lib\components\ui\overlay\PDFViewerModalOld.svelte", "Svelte", 0, 0, 0, 0, 136, 1, 19, 156
"c:\pdf-bak\src\lib\components\ui\utility\LanguageSelector.svelte", "Svelte", 0, 0, 0, 0, 79, 0, 9, 88
"c:\pdf-bak\src\lib\components\ui\utility\T.svelte", "Svelte", 0, 0, 0, 0, 9, 0, 4, 13
"c:\pdf-bak\src\lib\components\ui\utility\ThemeSwitcher.svelte", "Svelte", 0, 0, 0, 0, 75, 0, 9, 84
"c:\pdf-bak\src\lib\external-modules.d.ts", "TypeScript", 80, 0, 0, 0, 0, 3, 16, 99
"c:\pdf-bak\src\lib\index.ts", "TypeScript", 1, 0, 0, 0, 0, 0, 1, 2
"c:\pdf-bak\src\lib\server\api\claude-api.ts", "TypeScript", 351, 0, 0, 0, 0, 9, 65, 425
"c:\pdf-bak\src\lib\server\api\index.ts", "TypeScript", 1, 0, 0, 0, 0, 0, 1, 2
"c:\pdf-bak\src\lib\server\api\responseUtils.ts", "TypeScript", 81, 0, 0, 0, 0, 0, 21, 102
"c:\pdf-bak\src\lib\server\auth\auth.ts", "TypeScript", 99, 0, 0, 0, 0, 1, 20, 120
"c:\pdf-bak\src\lib\server\auth\index.ts", "TypeScript", 3, 0, 0, 0, 0, 0, 1, 4
"c:\pdf-bak\src\lib\server\auth\password.ts", "TypeScript", 29, 0, 0, 0, 0, 0, 6, 35
"c:\pdf-bak\src\lib\server\auth\passwordReset.ts", "TypeScript", 89, 0, 0, 0, 0, 1, 19, 109
"c:\pdf-bak\src\lib\server\db\helpers.ts", "TypeScript", 81, 0, 0, 0, 0, 0, 17, 98
"c:\pdf-bak\src\lib\server\db\index.ts", "TypeScript", 23, 0, 0, 0, 0, 0, 5, 28
"c:\pdf-bak\src\lib\server\db\schema.ts", "TypeScript", 158, 0, 0, 0, 0, 0, 19, 177
"c:\pdf-bak\src\lib\server\email\email.ts", "TypeScript", 47, 0, 0, 0, 0, 0, 9, 56
"c:\pdf-bak\src\lib\server\email\index.ts", "TypeScript", 1, 0, 0, 0, 0, 0, 1, 2
"c:\pdf-bak\src\lib\server\index.ts", "TypeScript", 5, 0, 0, 0, 0, 0, 1, 6
"c:\pdf-bak\src\lib\server\pdf\pdfAnalysis.ts", "TypeScript", 238, 0, 0, 0, 0, 0, 52, 290
"c:\pdf-bak\src\lib\server\storage\archiveExtractor.ts", "TypeScript", 333, 0, 0, 0, 0, 29, 63, 425
"c:\pdf-bak\src\lib\server\storage\archiveHandler.ts", "TypeScript", 201, 0, 0, 0, 0, 12, 52, 265
"c:\pdf-bak\src\lib\server\storage\fileStorage.ts", "TypeScript", 44, 0, 0, 0, 0, 0, 10, 54
"c:\pdf-bak\src\lib\server\storage\index.ts", "TypeScript", 2, 0, 0, 0, 0, 0, 1, 3
"c:\pdf-bak\src\lib\stores\loading.ts", "TypeScript", 19, 0, 0, 0, 0, 0, 4, 23
"c:\pdf-bak\src\lib\stores\locale.ts", "TypeScript", 834, 0, 0, 0, 0, 68, 73, 975
"c:\pdf-bak\src\lib\stores\theme.ts", "TypeScript", 24, 0, 0, 0, 0, 0, 6, 30
"c:\pdf-bak\src\lib\types.ts", "TypeScript", 107, 0, 0, 0, 0, 5, 14, 126
"c:\pdf-bak\src\lib\utils\auth.ts", "TypeScript", 39, 0, 0, 0, 0, 0, 12, 51
"c:\pdf-bak\src\lib\utils\complexityCalculator.ts", "TypeScript", 228, 0, 0, 0, 0, 0, 42, 270
"c:\pdf-bak\src\lib\utils\validation.ts", "TypeScript", 75, 0, 0, 0, 0, 3, 13, 91
"c:\pdf-bak\src\routes\+layout.server.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 1, 8
"c:\pdf-bak\src\routes\+layout.svelte", "Svelte", 0, 0, 0, 0, 32, 0, 4, 36
"c:\pdf-bak\src\routes\+page.svelte", "Svelte", 0, 0, 0, 0, 84, 0, 7, 91
"c:\pdf-bak\src\routes\api\analyze-pdf-text\+server.ts", "TypeScript", 49, 0, 0, 0, 0, 0, 7, 56
"c:\pdf-bak\src\routes\api\analyze-submission\+server.ts", "TypeScript", 150, 0, 0, 0, 0, 6, 30, 186
"c:\pdf-bak\src\routes\api\batch-process\+server.ts", "TypeScript", 77, 0, 0, 0, 0, 3, 15, 95
"c:\pdf-bak\src\routes\api\batch-process\download\[filename]\+server.ts", "TypeScript", 60, 0, 0, 0, 0, 0, 10, 70
"c:\pdf-bak\src\routes\api\check-cs-file\+server.ts", "TypeScript", 47, 0, 0, 0, 0, 2, 12, 61
"c:\pdf-bak\src\routes\api\download-cs-file\+server.ts", "TypeScript", 39, 0, 0, 0, 0, 0, 7, 46
"c:\pdf-bak\src\routes\api\download-selected-cs-files\+server.ts", "TypeScript", 61, 0, 0, 0, 0, 0, 13, 74
"c:\pdf-bak\src\routes\api\list-cs-files\+server.ts", "TypeScript", 62, 0, 0, 0, 0, 1, 14, 77
"c:\pdf-bak\src\routes\api\pdf\[...path]\+server.ts", "TypeScript", 38, 0, 0, 0, 0, 3, 8, 49
"c:\pdf-bak\src\routes\api\projects\[projectId]\+server.ts", "TypeScript", 30, 0, 0, 0, 0, 0, 6, 36
"c:\pdf-bak\src\routes\api\projects\[projectId]\assigned-students\+server.ts", "TypeScript", 42, 0, 0, 0, 0, 1, 5, 48
"c:\pdf-bak\src\routes\api\projects\[projectId]\groups\+server.ts", "TypeScript", 39, 0, 0, 0, 0, 1, 5, 45
"c:\pdf-bak\src\routes\api\submissions\[submissionId]\+server.ts", "TypeScript", 54, 0, 0, 0, 0, 0, 11, 65
"c:\pdf-bak\src\routes\auth\forgot-password\+page.server.ts", "TypeScript", 53, 0, 0, 0, 0, 0, 9, 62
"c:\pdf-bak\src\routes\auth\forgot-password\+page.svelte", "Svelte", 0, 0, 0, 0, 55, 0, 7, 62
"c:\pdf-bak\src\routes\auth\login\+page.server.ts", "TypeScript", 45, 0, 0, 0, 0, 0, 12, 57
"c:\pdf-bak\src\routes\auth\login\+page.svelte", "Svelte", 0, 0, 0, 0, 58, 0, 7, 65
"c:\pdf-bak\src\routes\auth\logout\+page.server.ts", "TypeScript", 12, 0, 0, 0, 0, 0, 3, 15
"c:\pdf-bak\src\routes\auth\register\+page.server.ts", "TypeScript", 130, 0, 0, 0, 0, 3, 21, 154
"c:\pdf-bak\src\routes\auth\register\+page.svelte", "Svelte", 0, 0, 0, 0, 135, 0, 17, 152
"c:\pdf-bak\src\routes\auth\reset-password\+page.server.ts", "TypeScript", 118, 0, 0, 0, 0, 3, 18, 139
"c:\pdf-bak\src\routes\auth\reset-password\+page.svelte", "Svelte", 0, 0, 0, 0, 102, 0, 12, 114
"c:\pdf-bak\src\routes\dashboard\+layout.server.ts", "TypeScript", 9, 0, 0, 0, 0, 0, 3, 12
"c:\pdf-bak\src\routes\dashboard\+layout.svelte", "Svelte", 0, 0, 0, 0, 6, 0, 1, 7
"c:\pdf-bak\src\routes\dashboard\+page.server.ts", "TypeScript", 7, 0, 0, 0, 0, 0, 1, 8
"c:\pdf-bak\src\routes\dashboard\+page.svelte", "Svelte", 0, 0, 0, 0, 238, 2, 23, 263
"c:\pdf-bak\src\routes\dashboard\admin\+page.server.ts", "TypeScript", 69, 0, 0, 0, 0, 2, 7, 78
"c:\pdf-bak\src\routes\dashboard\admin\+page.svelte", "Svelte", 0, 0, 0, 0, 61, 0, 7, 68
"c:\pdf-bak\src\routes\dashboard\admin\approvals\+page.server.ts", "TypeScript", 135, 0, 0, 0, 0, 2, 21, 158
"c:\pdf-bak\src\routes\dashboard\admin\approvals\+page.svelte", "Svelte", 0, 0, 0, 0, 133, 0, 8, 141
"c:\pdf-bak\src\routes\dashboard\admin\users\+page.server.ts", "TypeScript", 176, 0, 0, 0, 0, 1, 14, 191
"c:\pdf-bak\src\routes\dashboard\admin\users\+page.svelte", "Svelte", 0, 0, 0, 0, 194, 0, 17, 211
"c:\pdf-bak\src\routes\dashboard\admin\users\[userId]\+page.server.ts", "TypeScript", 60, 0, 0, 0, 0, 0, 11, 71
"c:\pdf-bak\src\routes\dashboard\admin\users\[userId]\+page.svelte", "Svelte", 0, 0, 0, 0, 135, 0, 16, 151
"c:\pdf-bak\src\routes\dashboard\admin\users\new\+page.server.ts", "TypeScript", 100, 0, 0, 0, 0, 0, 18, 118
"c:\pdf-bak\src\routes\dashboard\admin\users\new\+page.svelte", "Svelte", 0, 0, 0, 0, 97, 0, 12, 109
"c:\pdf-bak\src\routes\dashboard\developer\+page.server.ts", "TypeScript", 60, 0, 0, 0, 0, 1, 10, 71
"c:\pdf-bak\src\routes\dashboard\developer\+page.svelte", "Svelte", 0, 0, 0, 0, 144, 0, 15, 159
"c:\pdf-bak\src\routes\dashboard\developer\admins\+page.server.ts", "TypeScript", 77, 0, 0, 0, 0, 2, 11, 90
"c:\pdf-bak\src\routes\dashboard\developer\admins\+page.svelte", "Svelte", 0, 0, 0, 0, 170, 0, 17, 187
"c:\pdf-bak\src\routes\dashboard\developer\admins\[adminId]\+page.server.ts", "TypeScript", 171, 0, 0, 0, 0, 0, 30, 201
"c:\pdf-bak\src\routes\dashboard\developer\admins\[adminId]\+page.svelte", "Svelte", 0, 0, 0, 0, 196, 0, 23, 219
"c:\pdf-bak\src\routes\dashboard\developer\admins\new\+page.server.ts", "TypeScript", 126, 0, 0, 0, 0, 0, 20, 146
"c:\pdf-bak\src\routes\dashboard\developer\admins\new\+page.svelte", "Svelte", 0, 0, 0, 0, 105, 0, 11, 116
"c:\pdf-bak\src\routes\dashboard\developer\organizations\+page.server.ts", "TypeScript", 76, 0, 0, 0, 0, 0, 11, 87
"c:\pdf-bak\src\routes\dashboard\developer\organizations\+page.svelte", "Svelte", 0, 0, 0, 0, 159, 0, 14, 173
"c:\pdf-bak\src\routes\dashboard\developer\organizations\[organizationId]\+page.server.ts", "TypeScript", 94, 0, 0, 0, 0, 0, 18, 112
"c:\pdf-bak\src\routes\dashboard\developer\organizations\[organizationId]\+page.svelte", "Svelte", 0, 0, 0, 0, 179, 0, 17, 196
"c:\pdf-bak\src\routes\dashboard\developer\organizations\new\+page.server.ts", "TypeScript", 53, 0, 0, 0, 0, 1, 12, 66
"c:\pdf-bak\src\routes\dashboard\developer\organizations\new\+page.svelte", "Svelte", 0, 0, 0, 0, 82, 0, 9, 91
"c:\pdf-bak\src\routes\dashboard\developer\pdf-analysis\+page.server.ts", "TypeScript", 19, 0, 0, 0, 0, 0, 6, 25
"c:\pdf-bak\src\routes\dashboard\developer\pdf-analysis\+page.svelte", "Svelte", 0, 0, 0, 0, 365, 6, 25, 396
"c:\pdf-bak\src\routes\dashboard\developer\system\settings\+page.server.ts", "TypeScript", 197, 0, 0, 0, 0, 0, 41, 238
"c:\pdf-bak\src\routes\dashboard\developer\system\settings\+page.svelte", "Svelte", 0, 0, 0, 0, 324, 4, 33, 361
"c:\pdf-bak\src\routes\dashboard\lecturer\+page.server.ts", "TypeScript", 96, 0, 0, 0, 0, 4, 13, 113
"c:\pdf-bak\src\routes\dashboard\lecturer\+page.svelte", "Svelte", 0, 0, 0, 0, 144, 0, 10, 154
"c:\pdf-bak\src\routes\dashboard\lecturer\groups\+page.server.ts", "TypeScript", 107, 0, 0, 0, 0, 10, 19, 136
"c:\pdf-bak\src\routes\dashboard\lecturer\groups\+page.svelte", "Svelte", 0, 0, 0, 0, 119, 0, 9, 128
"c:\pdf-bak\src\routes\dashboard\lecturer\groups\[groupId]\+page.server.ts", "TypeScript", 584, 0, 0, 0, 0, 1, 102, 687
"c:\pdf-bak\src\routes\dashboard\lecturer\groups\[groupId]\+page.svelte", "Svelte", 0, 0, 0, 0, 530, 3, 40, 573
"c:\pdf-bak\src\routes\dashboard\lecturer\groups\new\+page.server.ts", "TypeScript", 153, 0, 0, 0, 0, 0, 19, 172
"c:\pdf-bak\src\routes\dashboard\lecturer\groups\new\+page.svelte", "Svelte", 0, 0, 0, 0, 335, 2, 35, 372
"c:\pdf-bak\src\routes\dashboard\lecturer\projects\+page.server.ts", "TypeScript", 55, 0, 0, 0, 0, 5, 11, 71
"c:\pdf-bak\src\routes\dashboard\lecturer\projects\+page.svelte", "Svelte", 0, 0, 0, 0, 126, 0, 9, 135
"c:\pdf-bak\src\routes\dashboard\lecturer\projects\[projectId]\+page.server.ts", "TypeScript", 477, 0, 0, 0, 0, 26, 77, 580
"c:\pdf-bak\src\routes\dashboard\lecturer\projects\[projectId]\+page.svelte", "Svelte", 0, 0, 0, 0, 837, 2, 99, 938
"c:\pdf-bak\src\routes\dashboard\lecturer\projects\new\+page.server.ts", "TypeScript", 148, 0, 0, 0, 0, 2, 21, 171
"c:\pdf-bak\src\routes\dashboard\lecturer\projects\new\+page.svelte", "Svelte", 0, 0, 0, 0, 272, 0, 30, 302
"c:\pdf-bak\src\routes\dashboard\student\+page.server.ts", "TypeScript", 80, 0, 0, 0, 0, 0, 7, 87
"c:\pdf-bak\src\routes\dashboard\student\+page.svelte", "Svelte", 0, 0, 0, 0, 121, 0, 7, 128
"c:\pdf-bak\src\routes\dashboard\student\groups\+page.server.ts", "TypeScript", 146, 0, 0, 0, 0, 14, 21, 181
"c:\pdf-bak\src\routes\dashboard\student\groups\+page.svelte", "Svelte", 0, 0, 0, 0, 135, 0, 10, 145
"c:\pdf-bak\src\routes\dashboard\student\groups\join\+page.server.ts", "TypeScript", 69, 0, 0, 0, 0, 0, 15, 84
"c:\pdf-bak\src\routes\dashboard\student\groups\join\+page.svelte", "Svelte", 0, 0, 0, 0, 60, 0, 11, 71
"c:\pdf-bak\src\routes\dashboard\student\projects\[projectId]\+page.server.ts", "TypeScript", 510, 0, 0, 0, 0, 12, 90, 612
"c:\pdf-bak\src\routes\dashboard\student\projects\[projectId]\+page.svelte", "Svelte", 0, 0, 0, 0, 588, 0, 60, 648
"c:\pdf-bak\src\routes\dashboard\student\submissions\+page.server.ts", "TypeScript", 186, 0, 0, 0, 0, 3, 36, 225
"c:\pdf-bak\src\routes\dashboard\student\submissions\+page.svelte", "Svelte", 0, 0, 0, 0, 127, 0, 11, 138
"c:\pdf-bak\src\routes\dashboard\student\submit\+page.server.ts", "TypeScript", 199, 0, 0, 0, 0, 5, 38, 242
"c:\pdf-bak\src\routes\dashboard\student\submit\+page.svelte", "Svelte", 0, 0, 0, 0, 96, 0, 7, 103
"c:\pdf-bak\src\routes\debug\+page.server.ts", "TypeScript", 13, 0, 0, 0, 0, 0, 2, 15
"c:\pdf-bak\src\routes\debug\+page.svelte", "Svelte", 0, 0, 0, 0, 32, 0, 9, 41
"c:\pdf-bak\src\routes\pending-approval\+page.server.ts", "TypeScript", 21, 0, 0, 0, 0, 1, 5, 27
"c:\pdf-bak\src\routes\pending-approval\+page.svelte", "Svelte", 0, 0, 0, 0, 32, 0, 4, 36
"c:\pdf-bak\src\routes\settings\+page.server.ts", "TypeScript", 149, 0, 0, 0, 0, 1, 29, 179
"c:\pdf-bak\src\routes\settings\+page.svelte", "Svelte", 0, 0, 0, 0, 193, 7, 26, 226
"c:\pdf-bak\src\variables.scss", "SCSS", 0, 8, 0, 0, 0, 1, 0, 9
"Total", "-", 8858, 8, 30, 764, 9660, 334, 2633, 22287