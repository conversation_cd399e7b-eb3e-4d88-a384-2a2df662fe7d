import { hashPassword } from '$lib/server/auth/password';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import { fail, redirect } from '@sveltejs/kit';
import { eq, and } from 'drizzle-orm';
import * as auth from '$lib/server/auth';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';
import { validateUsername, validateEmail, validatePassword, validateRole, validateName } from '$lib/utils/validation';
import { requireUnauthenticated, ROLE_ROUTES } from '$lib/utils/auth';

export const load: PageServerLoad = async (event) => {
	requireUnauthenticated(event.locals.user);

	let organizations = [];
	try {
		organizations = await db
			.select({
				id: table.organization.id,
				name: table.organization.name,
				description: table.organization.description
			})
			.from(table.organization)
			.where(eq(table.organization.isActive, true))
			.orderBy(table.organization.name);
	} catch (error) {
		console.error('Error getting active organizations, falling back to all:', error);
		organizations = await db
			.select({
				id: table.organization.id,
				name: table.organization.name,
				description: table.organization.description
			})
			.from(table.organization)
			.orderBy(table.organization.name);
	}

	return {
		organizations
	};
};

export const actions: Actions = {
	register: async (event) => {
		const formData = await event.request.formData();
		const username = formData.get('username');
		const name = formData.get('name');
		const email = formData.get('email');
		const password = formData.get('password');
		const role = formData.get('role');
		const organization = formData.get('organization');

		// validate fiel entries
		if (!validateUsername(username)) {
			return fail(400, { message: 'Invalid username (min 3, max 31 characters, alphanumeric only)' });
		}
		if (!validateName(name)) {
			return fail(400, { message: 'Full name is required and must be 2-100 characters' });
		}
		if (!validateEmail(email)) {
			return fail(400, { message: 'Invalid email address' });
		}
		if (!validatePassword(password)) {
			return fail(400, { message: 'Invalid password (min 6, max 255 characters)' });
		}
		if (!validateRole(role)) {
			return fail(400, { message: 'Invalid role selected' });
		}
		if (!organization) {
			return fail(400, { message: 'Please select an organization' });
		}

		// does username or email already exists
		const existingUsers = await db
			.select({ id: table.user.id })
			.from(table.user)
			.where(eq(table.user.username, username));

		if (existingUsers.length > 0) {
			return fail(400, { message: 'Username already taken' });
		}

		const existingEmails = await db
			.select({ id: table.user.id })
			.from(table.user)
			.where(eq(table.user.email, email));

		if (existingEmails.length > 0) {
			return fail(400, { message: 'Email already registered' });
		}

		const [selectedOrg] = await db
			.select()
			.from(table.organization)
			.where(
				and(
					eq(table.organization.id, organization as string),
					eq(table.organization.isActive, true),
					eq(table.organization.isFrozen, false)
				)
			);

		if (!selectedOrg) {
			return fail(400, { message: 'Selected organization is invalid or unavailable' });
		}

		const userId = generateUserId();

		const passwordHash = await hashPassword(password);

		const isApproved = role === 'student';

		try {
			await db.insert(table.user).values({
				id: userId,
				username,
				name: name as string,
				email,
				passwordHash,
				role: role as 'student' | 'lecturer' | 'admin' | 'developer',
				organization: organization as string,
				isActive: true,
				isApproved
			});

			const sessionToken = auth.generateSessionToken();
			const session = await auth.createSession(sessionToken, userId);
			auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);

			const user = {
				id: userId,
				role: role as 'student' | 'lecturer' | 'admin' | 'developer',
				isApproved
			};
			const redirectPath = user.role === 'lecturer' && !user.isApproved
				? '/pending-approval'
				: ROLE_ROUTES[user.role] || '/';

			return { success: true, redirectTo: redirectPath };
		} catch (e) {
			console.error('Registration error:', e);
			return fail(500, { message: 'An error occurred during registration. Please try again.' });
		}
	}
};

function generateUserId() {
	// ID with 120 bits of entropy, or about the same as UUID v4.
	const bytes = crypto.getRandomValues(new Uint8Array(15));
	const id = encodeBase32LowerCase(bytes);
	return id;
}

