import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const UPLOADS_BASE_DIR = path.resolve(__dirname, '../../../../uploads');
const TEMP_DIR = path.resolve(__dirname, '../../../../temp');

if (!fs.existsSync(UPLOADS_BASE_DIR)) {
  fs.mkdirSync(UPLOADS_BASE_DIR, { recursive: true });
}

if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

export function ensureUserUploadDir(userId: string): string {
  const userDir = path.join(UPLOADS_BASE_DIR, userId);
  if (!fs.existsSync(userDir)) {
    fs.mkdirSync(userDir, { recursive: true });
  }
  return userDir;
}

export async function saveFile(userId: string, file: any, originalFilename: string): Promise<string> {
  const userDir = ensureUserUploadDir(userId);
  const timestamp = Date.now();
  const safeFilename = originalFilename.replace(/[^a-zA-Z0-9.-]/g, '_');
  const filename = `${timestamp}_${safeFilename}`;
  const filePath = path.join(userDir, filename);

  const buffer = await getBufferFromFile(file);
  fs.writeFileSync(filePath, buffer);
  return `/uploads/${userId}/${filename}`;
}

async function getBufferFromFile(file: any): Promise<Buffer> {
  if (file.arrayBuffer) return Buffer.from(await file.arrayBuffer());
  if (Buffer.isBuffer(file)) return file;
  if (file.buffer) return Buffer.from(file.buffer);
  return Buffer.from(file);
}

export function getAbsoluteFilePath(relativePath: string): string {
  return path.join(
    path.dirname(UPLOADS_BASE_DIR),
    relativePath.startsWith('/') ? relativePath.substring(1) : relativePath
  );
}

export function fileExists(relativePath: string): boolean {
  return fs.existsSync(getAbsoluteFilePath(relativePath));
}
