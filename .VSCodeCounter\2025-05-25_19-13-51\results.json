{"file:///c%3A/pdf-bak/src/app.d.ts": {"language": "TypeScript", "code": 19, "comment": 2, "blank": 5}, "file:///c%3A/pdf-bak/src/hooks.server.ts": {"language": "TypeScript", "code": 63, "comment": 13, "blank": 15}, "file:///c%3A/pdf-bak/src/lib/external-modules.d.ts": {"language": "TypeScript", "code": 80, "comment": 3, "blank": 16}, "file:///c%3A/pdf-bak/src/lib/stores/locale.ts": {"language": "TypeScript", "code": 834, "comment": 68, "blank": 73}, "file:///c%3A/pdf-bak/src/routes/%2Blayout.server.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/lib/stores/theme.ts": {"language": "TypeScript", "code": 24, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/lib/stores/loading.ts": {"language": "TypeScript", "code": 19, "comment": 0, "blank": 4}, "file:///c%3A/pdf-bak/src/routes/%2Blayout.svelte": {"language": "Svelte", "code": 32, "comment": 0, "blank": 4}, "file:///c%3A/pdf-bak/src/routes/%2Bpage.svelte": {"language": "Svelte", "code": 84, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/lib/utils/auth.ts": {"language": "TypeScript", "code": 39, "comment": 0, "blank": 12}, "file:///c%3A/pdf-bak/src/lib/utils/validation.ts": {"language": "TypeScript", "code": 75, "comment": 3, "blank": 13}, "file:///c%3A/pdf-bak/src/lib/utils/complexityCalculator.ts": {"language": "TypeScript", "code": 228, "comment": 0, "blank": 42}, "file:///c%3A/pdf-bak/src/routes/debug/%2Bpage.svelte": {"language": "Svelte", "code": 32, "comment": 0, "blank": 9}, "file:///c%3A/pdf-bak/src/routes/debug/%2Bpage.server.ts": {"language": "TypeScript", "code": 13, "comment": 0, "blank": 2}, "file:///c%3A/pdf-bak/src/routes/dashboard/%2Blayout.server.ts": {"language": "TypeScript", "code": 9, "comment": 0, "blank": 3}, "file:///c%3A/pdf-bak/src/routes/dashboard/%2Blayout.svelte": {"language": "Svelte", "code": 6, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/routes/dashboard/%2Bpage.server.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/routes/dashboard/%2Bpage.svelte": {"language": "Svelte", "code": 238, "comment": 2, "blank": 23}, "file:///c%3A/pdf-bak/src/routes/settings/%2Bpage.svelte": {"language": "Svelte", "code": 193, "comment": 7, "blank": 26}, "file:///c%3A/pdf-bak/src/routes/settings/%2Bpage.server.ts": {"language": "TypeScript", "code": 149, "comment": 1, "blank": 29}, "file:///c%3A/pdf-bak/src/routes/pending-approval/%2Bpage.svelte": {"language": "Svelte", "code": 32, "comment": 0, "blank": 4}, "file:///c%3A/pdf-bak/src/routes/pending-approval/%2Bpage.server.ts": {"language": "TypeScript", "code": 21, "comment": 1, "blank": 5}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/%2Bpage.svelte": {"language": "Svelte", "code": 121, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/%2Bpage.server.ts": {"language": "TypeScript", "code": 80, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/routes/auth/reset-password/%2Bpage.svelte": {"language": "Svelte", "code": 102, "comment": 0, "blank": 12}, "file:///c%3A/pdf-bak/src/routes/auth/reset-password/%2Bpage.server.ts": {"language": "TypeScript", "code": 118, "comment": 3, "blank": 18}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/%2Bpage.svelte": {"language": "Svelte", "code": 144, "comment": 0, "blank": 10}, "file:///c%3A/pdf-bak/src/routes/auth/register/%2Bpage.svelte": {"language": "Svelte", "code": 135, "comment": 0, "blank": 17}, "file:///c%3A/pdf-bak/src/routes/auth/register/%2Bpage.server.ts": {"language": "TypeScript", "code": 130, "comment": 3, "blank": 21}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/projects/%2Bpage.server.ts": {"language": "TypeScript", "code": 55, "comment": 5, "blank": 11}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/groups/%2Bpage.svelte": {"language": "Svelte", "code": 119, "comment": 0, "blank": 9}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/groups/new/%2Bpage.svelte": {"language": "Svelte", "code": 335, "comment": 2, "blank": 35}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/groups/new/%2Bpage.server.ts": {"language": "TypeScript", "code": 153, "comment": 0, "blank": 19}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/projects/%2Bpage.svelte": {"language": "Svelte", "code": 126, "comment": 0, "blank": 9}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/groups/%2Bpage.server.ts": {"language": "TypeScript", "code": 107, "comment": 10, "blank": 19}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/projects/new/%2Bpage.svelte": {"language": "Svelte", "code": 272, "comment": 0, "blank": 30}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/projects/new/%2Bpage.server.ts": {"language": "TypeScript", "code": 148, "comment": 2, "blank": 21}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/groups/%5BgroupId%5D/%2Bpage.svelte": {"language": "Svelte", "code": 530, "comment": 3, "blank": 40}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/groups/%5BgroupId%5D/%2Bpage.server.ts": {"language": "TypeScript", "code": 584, "comment": 1, "blank": 102}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/%2Bpage.server.ts": {"language": "TypeScript", "code": 96, "comment": 4, "blank": 13}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/projects/%5BprojectId%5D/%2Bpage.svelte": {"language": "Svelte", "code": 837, "comment": 2, "blank": 99}, "file:///c%3A/pdf-bak/src/routes/dashboard/lecturer/projects/%5BprojectId%5D/%2Bpage.server.ts": {"language": "TypeScript", "code": 477, "comment": 26, "blank": 77}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/submit/%2Bpage.server.ts": {"language": "TypeScript", "code": 199, "comment": 5, "blank": 38}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/submit/%2Bpage.svelte": {"language": "Svelte", "code": 96, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/submissions/%2Bpage.server.ts": {"language": "TypeScript", "code": 186, "comment": 3, "blank": 36}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/submissions/%2Bpage.svelte": {"language": "Svelte", "code": 127, "comment": 0, "blank": 11}, "file:///c%3A/pdf-bak/src/routes/auth/logout/%2Bpage.server.ts": {"language": "TypeScript", "code": 12, "comment": 0, "blank": 3}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/groups/%2Bpage.svelte": {"language": "Svelte", "code": 135, "comment": 0, "blank": 10}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/groups/%2Bpage.server.ts": {"language": "TypeScript", "code": 146, "comment": 14, "blank": 21}, "file:///c%3A/pdf-bak/src/routes/auth/forgot-password/%2Bpage.svelte": {"language": "Svelte", "code": 55, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/routes/auth/forgot-password/%2Bpage.server.ts": {"language": "TypeScript", "code": 53, "comment": 0, "blank": 9}, "file:///c%3A/pdf-bak/src/routes/auth/login/%2Bpage.svelte": {"language": "Svelte", "code": 58, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/routes/auth/login/%2Bpage.server.ts": {"language": "TypeScript", "code": 45, "comment": 0, "blank": 12}, "file:///c%3A/pdf-bak/src/lib/types.ts": {"language": "TypeScript", "code": 107, "comment": 5, "blank": 14}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/projects/%5BprojectId%5D/%2Bpage.svelte": {"language": "Svelte", "code": 588, "comment": 0, "blank": 60}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/projects/%5BprojectId%5D/%2Bpage.server.ts": {"language": "TypeScript", "code": 510, "comment": 12, "blank": 90}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/groups/join/%2Bpage.svelte": {"language": "Svelte", "code": 60, "comment": 0, "blank": 11}, "file:///c%3A/pdf-bak/src/routes/dashboard/student/groups/join/%2Bpage.server.ts": {"language": "TypeScript", "code": 69, "comment": 0, "blank": 15}, "file:///c%3A/pdf-bak/src/lib/index.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/%2Bpage.svelte": {"language": "Svelte", "code": 144, "comment": 0, "blank": 15}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/%2Bpage.server.ts": {"language": "TypeScript", "code": 60, "comment": 1, "blank": 10}, "file:///c%3A/pdf-bak/src/lib/actions/clickOutside.ts": {"language": "TypeScript", "code": 14, "comment": 0, "blank": 4}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/%2Bpage.server.ts": {"language": "TypeScript", "code": 69, "comment": 2, "blank": 7}, "file:///c%3A/pdf-bak/src/lib/components/ui/index.ts": {"language": "TypeScript", "code": 34, "comment": 0, "blank": 8}, "file:///c%3A/pdf-bak/src/lib/components/ui/utility/ThemeSwitcher.svelte": {"language": "Svelte", "code": 75, "comment": 0, "blank": 9}, "file:///c%3A/pdf-bak/src/lib/components/ui/navigation/ActionButton.svelte": {"language": "Svelte", "code": 50, "comment": 0, "blank": 4}, "file:///c%3A/pdf-bak/src/lib/components/ui/overlay/BatchProcessModal.svelte": {"language": "Svelte", "code": 213, "comment": 0, "blank": 26}, "file:///c%3A/pdf-bak/src/routes/api/submissions/%5BsubmissionId%5D/%2Bserver.ts": {"language": "TypeScript", "code": 54, "comment": 0, "blank": 11}, "file:///c%3A/pdf-bak/src/lib/components/ui/navigation/BackButton.svelte": {"language": "Svelte", "code": 30, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/lib/components/ui/utility/T.svelte": {"language": "Svelte", "code": 9, "comment": 0, "blank": 4}, "file:///c%3A/pdf-bak/src/lib/components/ui/overlay/Modal.svelte": {"language": "Svelte", "code": 82, "comment": 0, "blank": 13}, "file:///c%3A/pdf-bak/src/lib/components/ui/utility/LanguageSelector.svelte": {"language": "Svelte", "code": 79, "comment": 0, "blank": 9}, "file:///c%3A/pdf-bak/src/lib/components/ui/overlay/PDFViewerModal.svelte": {"language": "Svelte", "code": 212, "comment": 2, "blank": 24}, "file:///c%3A/pdf-bak/src/lib/components/ui/navigation/ViewAllLink.svelte": {"language": "Svelte", "code": 19, "comment": 0, "blank": 3}, "file:///c%3A/pdf-bak/src/lib/components/ui/overlay/ManualTableEntryModal.svelte": {"language": "Svelte", "code": 157, "comment": 0, "blank": 22}, "file:///c%3A/pdf-bak/src/lib/components/ui/navigation/NavLink.svelte": {"language": "Svelte", "code": 71, "comment": 0, "blank": 8}, "file:///c%3A/pdf-bak/src/lib/components/ui/layout/PageHeader.svelte": {"language": "Svelte", "code": 35, "comment": 0, "blank": 3}, "file:///c%3A/pdf-bak/src/routes/api/projects/%5BprojectId%5D/%2Bserver.ts": {"language": "TypeScript", "code": 30, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/lib/components/ui/layout/Header.svelte": {"language": "Svelte", "code": 382, "comment": 5, "blank": 31}, "file:///c%3A/pdf-bak/src/lib/components/ui/layout/FormLayout.svelte": {"language": "Svelte", "code": 83, "comment": 0, "blank": 10}, "file:///c%3A/pdf-bak/src/lib/components/ui/layout/DashboardSection.svelte": {"language": "Svelte", "code": 42, "comment": 0, "blank": 5}, "file:///c%3A/pdf-bak/src/lib/components/ui/layout/Card.svelte": {"language": "Svelte", "code": 42, "comment": 0, "blank": 5}, "file:///c%3A/pdf-bak/src/lib/components/ui/layout/AuthLayout.svelte": {"language": "Svelte", "code": 44, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/lib/components/ui/input/FormInput.svelte": {"language": "Svelte", "code": 82, "comment": 0, "blank": 5}, "file:///c%3A/pdf-bak/src/lib/components/ui/overlay/PDFViewerModalOld.svelte": {"language": "Svelte", "code": 136, "comment": 1, "blank": 19}, "file:///c%3A/pdf-bak/src/routes/api/projects/%5BprojectId%5D/assigned-students/%2Bserver.ts": {"language": "TypeScript", "code": 42, "comment": 1, "blank": 5}, "file:///c%3A/pdf-bak/src/routes/api/projects/%5BprojectId%5D/groups/%2Bserver.ts": {"language": "TypeScript", "code": 39, "comment": 1, "blank": 5}, "file:///c%3A/pdf-bak/src/lib/components/ui/data/DataTable.svelte": {"language": "Svelte", "code": 38, "comment": 0, "blank": 2}, "file:///c%3A/pdf-bak/src/lib/components/ui/input/FormSelect.svelte": {"language": "Svelte", "code": 71, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/routes/api/pdf/%5B...path%5D/%2Bserver.ts": {"language": "TypeScript", "code": 38, "comment": 3, "blank": 8}, "file:///c%3A/pdf-bak/src/lib/components/ui/input/ManualTableInput.svelte": {"language": "Svelte", "code": 191, "comment": 0, "blank": 18}, "file:///c%3A/pdf-bak/src/lib/components/ui/data/DetailItem.svelte": {"language": "Svelte", "code": 10, "comment": 0, "blank": 2}, "file:///c%3A/pdf-bak/src/lib/server/storage/index.ts": {"language": "TypeScript", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/lib/components/ui/feedback/Countdown.svelte": {"language": "Svelte", "code": 85, "comment": 0, "blank": 23}, "file:///c%3A/pdf-bak/src/lib/components/ui/data/ListItem.svelte": {"language": "Svelte", "code": 12, "comment": 0, "blank": 2}, "file:///c%3A/pdf-bak/src/lib/components/ui/feedback/EmptyState.svelte": {"language": "Svelte", "code": 40, "comment": 0, "blank": 5}, "file:///c%3A/pdf-bak/src/lib/components/ui/data/StatLink.svelte": {"language": "Svelte", "code": 41, "comment": 0, "blank": 2}, "file:///c%3A/pdf-bak/src/lib/components/ui/data/StatTile.svelte": {"language": "Svelte", "code": 87, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/lib/components/ui/feedback/StatusBadge.svelte": {"language": "Svelte", "code": 22, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/lib/components/ui/feedback/LoadingIndicator.svelte": {"language": "Svelte", "code": 49, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/lib/components/ui/data/StatCard.svelte": {"language": "Svelte", "code": 27, "comment": 0, "blank": 3}, "file:///c%3A/pdf-bak/src/lib/components/ui/button/Button.svelte": {"language": "Svelte", "code": 57, "comment": 0, "blank": 4}, "file:///c%3A/pdf-bak/src/lib/components/ui/data/KeyValueList.svelte": {"language": "Svelte", "code": 20, "comment": 0, "blank": 2}, "file:///c%3A/pdf-bak/src/lib/components/ui/input/PasswordStrengthMeter.svelte": {"language": "Svelte", "code": 175, "comment": 0, "blank": 23}, "file:///c%3A/pdf-bak/src/lib/components/ui/feedback/AlertMessage.svelte": {"language": "Svelte", "code": 91, "comment": 0, "blank": 11}, "file:///c%3A/pdf-bak/src/lib/server/storage/fileStorage.ts": {"language": "TypeScript", "code": 44, "comment": 0, "blank": 10}, "file:///c%3A/pdf-bak/src/lib/server/storage/archiveHandler.ts": {"language": "TypeScript", "code": 201, "comment": 12, "blank": 52}, "file:///c%3A/pdf-bak/src/lib/server/storage/archiveExtractor.ts": {"language": "TypeScript", "code": 333, "comment": 29, "blank": 63}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/%2Bpage.svelte": {"language": "Svelte", "code": 61, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/routes/api/download-cs-file/%2Bserver.ts": {"language": "TypeScript", "code": 39, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/routes/api/list-cs-files/%2Bserver.ts": {"language": "TypeScript", "code": 62, "comment": 1, "blank": 14}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/approvals/%2Bpage.svelte": {"language": "Svelte", "code": 133, "comment": 0, "blank": 8}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/users/%2Bpage.svelte": {"language": "Svelte", "code": 194, "comment": 0, "blank": 17}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/approvals/%2Bpage.server.ts": {"language": "TypeScript", "code": 135, "comment": 2, "blank": 21}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/users/%2Bpage.server.ts": {"language": "TypeScript", "code": 176, "comment": 1, "blank": 14}, "file:///c%3A/pdf-bak/src/lib/server/pdf/pdfAnalysis.ts": {"language": "TypeScript", "code": 238, "comment": 0, "blank": 52}, "file:///c%3A/pdf-bak/src/lib/server/index.ts": {"language": "TypeScript", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/routes/api/check-cs-file/%2Bserver.ts": {"language": "TypeScript", "code": 47, "comment": 2, "blank": 12}, "file:///c%3A/pdf-bak/src/lib/server/email/index.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/users/new/%2Bpage.server.ts": {"language": "TypeScript", "code": 100, "comment": 0, "blank": 18}, "file:///c%3A/pdf-bak/src/routes/api/download-selected-cs-files/%2Bserver.ts": {"language": "TypeScript", "code": 61, "comment": 0, "blank": 13}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/users/%5BuserId%5D/%2Bpage.svelte": {"language": "Svelte", "code": 135, "comment": 0, "blank": 16}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/users/%5BuserId%5D/%2Bpage.server.ts": {"language": "TypeScript", "code": 60, "comment": 0, "blank": 11}, "file:///c%3A/pdf-bak/src/routes/dashboard/admin/users/new/%2Bpage.svelte": {"language": "Svelte", "code": 97, "comment": 0, "blank": 12}, "file:///c%3A/pdf-bak/src/lib/server/email/email.ts": {"language": "TypeScript", "code": 47, "comment": 0, "blank": 9}, "file:///c%3A/pdf-bak/src/routes/api/batch-process/%2Bserver.ts": {"language": "TypeScript", "code": 77, "comment": 3, "blank": 15}, "file:///c%3A/pdf-bak/src/lib/server/db/schema.ts": {"language": "TypeScript", "code": 158, "comment": 0, "blank": 19}, "file:///c%3A/pdf-bak/src/lib/server/auth/passwordReset.ts": {"language": "TypeScript", "code": 89, "comment": 1, "blank": 19}, "file:///c%3A/pdf-bak/src/lib/server/auth/index.ts": {"language": "TypeScript", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/lib/server/auth/auth.ts": {"language": "TypeScript", "code": 99, "comment": 1, "blank": 20}, "file:///c%3A/pdf-bak/src/lib/server/db/helpers.ts": {"language": "TypeScript", "code": 81, "comment": 0, "blank": 17}, "file:///c%3A/pdf-bak/src/lib/server/db/index.ts": {"language": "TypeScript", "code": 23, "comment": 0, "blank": 5}, "file:///c%3A/pdf-bak/src/lib/server/api/index.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/pdf-bak/src/lib/server/auth/password.ts": {"language": "TypeScript", "code": 29, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/lib/server/api/responseUtils.ts": {"language": "TypeScript", "code": 81, "comment": 0, "blank": 21}, "file:///c%3A/pdf-bak/src/lib/server/api/claude-api.ts": {"language": "TypeScript", "code": 351, "comment": 9, "blank": 65}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/pdf-analysis/%2Bpage.server.ts": {"language": "TypeScript", "code": 19, "comment": 0, "blank": 6}, "file:///c%3A/pdf-bak/src/routes/api/analyze-pdf-text/%2Bserver.ts": {"language": "TypeScript", "code": 49, "comment": 0, "blank": 7}, "file:///c%3A/pdf-bak/src/routes/api/batch-process/download/%5Bfilename%5D/%2Bserver.ts": {"language": "TypeScript", "code": 60, "comment": 0, "blank": 10}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/organizations/%2Bpage.svelte": {"language": "Svelte", "code": 159, "comment": 0, "blank": 14}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/system/settings/%2Bpage.server.ts": {"language": "TypeScript", "code": 197, "comment": 0, "blank": 41}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/system/settings/%2Bpage.svelte": {"language": "Svelte", "code": 324, "comment": 4, "blank": 33}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/organizations/%5BorganizationId%5D/%2Bpage.svelte": {"language": "Svelte", "code": 179, "comment": 0, "blank": 17}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/admins/%2Bpage.server.ts": {"language": "TypeScript", "code": 77, "comment": 2, "blank": 11}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/pdf-analysis/%2Bpage.svelte": {"language": "Svelte", "code": 365, "comment": 6, "blank": 25}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/admins/%5BadminId%5D/%2Bpage.svelte": {"language": "Svelte", "code": 196, "comment": 0, "blank": 23}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/admins/new/%2Bpage.svelte": {"language": "Svelte", "code": 105, "comment": 0, "blank": 11}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/admins/new/%2Bpage.server.ts": {"language": "TypeScript", "code": 126, "comment": 0, "blank": 20}, "file:///c%3A/pdf-bak/src/routes/api/analyze-submission/%2Bserver.ts": {"language": "TypeScript", "code": 150, "comment": 6, "blank": 30}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/organizations/%5BorganizationId%5D/%2Bpage.server.ts": {"language": "TypeScript", "code": 94, "comment": 0, "blank": 18}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/admins/%5BadminId%5D/%2Bpage.server.ts": {"language": "TypeScript", "code": 171, "comment": 0, "blank": 30}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/organizations/%2Bpage.server.ts": {"language": "TypeScript", "code": 76, "comment": 0, "blank": 11}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/admins/%2Bpage.svelte": {"language": "Svelte", "code": 170, "comment": 0, "blank": 17}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/organizations/new/%2Bpage.server.ts": {"language": "TypeScript", "code": 53, "comment": 1, "blank": 12}, "file:///c%3A/pdf-bak/src/routes/dashboard/developer/organizations/new/%2Bpage.svelte": {"language": "Svelte", "code": 82, "comment": 0, "blank": 9}, "file:///c%3A/pdf-bak/src/app.css": {"language": "PostCSS", "code": 764, "comment": 37, "blank": 97}, "file:///c%3A/pdf-bak/src/variables.scss": {"language": "SCSS", "code": 8, "comment": 1, "blank": 0}, "file:///c%3A/pdf-bak/src/app.html": {"language": "HTML", "code": 30, "comment": 0, "blank": 2}}