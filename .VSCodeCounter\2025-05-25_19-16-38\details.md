# Details

Date : 2025-05-25 19:16:38

Directory c:\\pdf-bak\\src

Total : 158 files,  19320 codes, 334 comments, 2633 blanks, all 22287 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [src/app.css](/src/app.css) | PostCSS | 764 | 37 | 97 | 898 |
| [src/app.d.ts](/src/app.d.ts) | TypeScript | 19 | 2 | 5 | 26 |
| [src/app.html](/src/app.html) | HTML | 30 | 0 | 2 | 32 |
| [src/hooks.server.ts](/src/hooks.server.ts) | TypeScript | 63 | 13 | 15 | 91 |
| [src/lib/actions/clickOutside.ts](/src/lib/actions/clickOutside.ts) | TypeScript | 14 | 0 | 4 | 18 |
| [src/lib/components/ui/button/Button.svelte](/src/lib/components/ui/button/Button.svelte) | Svelte | 57 | 0 | 4 | 61 |
| [src/lib/components/ui/data/DataTable.svelte](/src/lib/components/ui/data/DataTable.svelte) | Svelte | 38 | 0 | 2 | 40 |
| [src/lib/components/ui/data/DetailItem.svelte](/src/lib/components/ui/data/DetailItem.svelte) | Svelte | 10 | 0 | 2 | 12 |
| [src/lib/components/ui/data/KeyValueList.svelte](/src/lib/components/ui/data/KeyValueList.svelte) | Svelte | 20 | 0 | 2 | 22 |
| [src/lib/components/ui/data/ListItem.svelte](/src/lib/components/ui/data/ListItem.svelte) | Svelte | 12 | 0 | 2 | 14 |
| [src/lib/components/ui/data/StatCard.svelte](/src/lib/components/ui/data/StatCard.svelte) | Svelte | 27 | 0 | 3 | 30 |
| [src/lib/components/ui/data/StatLink.svelte](/src/lib/components/ui/data/StatLink.svelte) | Svelte | 41 | 0 | 2 | 43 |
| [src/lib/components/ui/data/StatTile.svelte](/src/lib/components/ui/data/StatTile.svelte) | Svelte | 87 | 0 | 6 | 93 |
| [src/lib/components/ui/feedback/AlertMessage.svelte](/src/lib/components/ui/feedback/AlertMessage.svelte) | Svelte | 91 | 0 | 11 | 102 |
| [src/lib/components/ui/feedback/Countdown.svelte](/src/lib/components/ui/feedback/Countdown.svelte) | Svelte | 85 | 0 | 23 | 108 |
| [src/lib/components/ui/feedback/EmptyState.svelte](/src/lib/components/ui/feedback/EmptyState.svelte) | Svelte | 40 | 0 | 5 | 45 |
| [src/lib/components/ui/feedback/LoadingIndicator.svelte](/src/lib/components/ui/feedback/LoadingIndicator.svelte) | Svelte | 49 | 0 | 6 | 55 |
| [src/lib/components/ui/feedback/StatusBadge.svelte](/src/lib/components/ui/feedback/StatusBadge.svelte) | Svelte | 22 | 0 | 6 | 28 |
| [src/lib/components/ui/index.ts](/src/lib/components/ui/index.ts) | TypeScript | 34 | 0 | 8 | 42 |
| [src/lib/components/ui/input/FormInput.svelte](/src/lib/components/ui/input/FormInput.svelte) | Svelte | 82 | 0 | 5 | 87 |
| [src/lib/components/ui/input/FormSelect.svelte](/src/lib/components/ui/input/FormSelect.svelte) | Svelte | 71 | 0 | 6 | 77 |
| [src/lib/components/ui/input/ManualTableInput.svelte](/src/lib/components/ui/input/ManualTableInput.svelte) | Svelte | 191 | 0 | 18 | 209 |
| [src/lib/components/ui/input/PasswordStrengthMeter.svelte](/src/lib/components/ui/input/PasswordStrengthMeter.svelte) | Svelte | 175 | 0 | 23 | 198 |
| [src/lib/components/ui/layout/AuthLayout.svelte](/src/lib/components/ui/layout/AuthLayout.svelte) | Svelte | 44 | 0 | 6 | 50 |
| [src/lib/components/ui/layout/Card.svelte](/src/lib/components/ui/layout/Card.svelte) | Svelte | 42 | 0 | 5 | 47 |
| [src/lib/components/ui/layout/DashboardSection.svelte](/src/lib/components/ui/layout/DashboardSection.svelte) | Svelte | 42 | 0 | 5 | 47 |
| [src/lib/components/ui/layout/FormLayout.svelte](/src/lib/components/ui/layout/FormLayout.svelte) | Svelte | 83 | 0 | 10 | 93 |
| [src/lib/components/ui/layout/Header.svelte](/src/lib/components/ui/layout/Header.svelte) | Svelte | 382 | 5 | 31 | 418 |
| [src/lib/components/ui/layout/PageHeader.svelte](/src/lib/components/ui/layout/PageHeader.svelte) | Svelte | 35 | 0 | 3 | 38 |
| [src/lib/components/ui/navigation/ActionButton.svelte](/src/lib/components/ui/navigation/ActionButton.svelte) | Svelte | 50 | 0 | 4 | 54 |
| [src/lib/components/ui/navigation/BackButton.svelte](/src/lib/components/ui/navigation/BackButton.svelte) | Svelte | 30 | 0 | 6 | 36 |
| [src/lib/components/ui/navigation/NavLink.svelte](/src/lib/components/ui/navigation/NavLink.svelte) | Svelte | 71 | 0 | 8 | 79 |
| [src/lib/components/ui/navigation/ViewAllLink.svelte](/src/lib/components/ui/navigation/ViewAllLink.svelte) | Svelte | 19 | 0 | 3 | 22 |
| [src/lib/components/ui/overlay/BatchProcessModal.svelte](/src/lib/components/ui/overlay/BatchProcessModal.svelte) | Svelte | 213 | 0 | 26 | 239 |
| [src/lib/components/ui/overlay/ManualTableEntryModal.svelte](/src/lib/components/ui/overlay/ManualTableEntryModal.svelte) | Svelte | 157 | 0 | 22 | 179 |
| [src/lib/components/ui/overlay/Modal.svelte](/src/lib/components/ui/overlay/Modal.svelte) | Svelte | 82 | 0 | 13 | 95 |
| [src/lib/components/ui/overlay/PDFViewerModal.svelte](/src/lib/components/ui/overlay/PDFViewerModal.svelte) | Svelte | 212 | 2 | 24 | 238 |
| [src/lib/components/ui/overlay/PDFViewerModalOld.svelte](/src/lib/components/ui/overlay/PDFViewerModalOld.svelte) | Svelte | 136 | 1 | 19 | 156 |
| [src/lib/components/ui/utility/LanguageSelector.svelte](/src/lib/components/ui/utility/LanguageSelector.svelte) | Svelte | 79 | 0 | 9 | 88 |
| [src/lib/components/ui/utility/T.svelte](/src/lib/components/ui/utility/T.svelte) | Svelte | 9 | 0 | 4 | 13 |
| [src/lib/components/ui/utility/ThemeSwitcher.svelte](/src/lib/components/ui/utility/ThemeSwitcher.svelte) | Svelte | 75 | 0 | 9 | 84 |
| [src/lib/external-modules.d.ts](/src/lib/external-modules.d.ts) | TypeScript | 80 | 3 | 16 | 99 |
| [src/lib/index.ts](/src/lib/index.ts) | TypeScript | 1 | 0 | 1 | 2 |
| [src/lib/server/api/claude-api.ts](/src/lib/server/api/claude-api.ts) | TypeScript | 351 | 9 | 65 | 425 |
| [src/lib/server/api/index.ts](/src/lib/server/api/index.ts) | TypeScript | 1 | 0 | 1 | 2 |
| [src/lib/server/api/responseUtils.ts](/src/lib/server/api/responseUtils.ts) | TypeScript | 81 | 0 | 21 | 102 |
| [src/lib/server/auth/auth.ts](/src/lib/server/auth/auth.ts) | TypeScript | 99 | 1 | 20 | 120 |
| [src/lib/server/auth/index.ts](/src/lib/server/auth/index.ts) | TypeScript | 3 | 0 | 1 | 4 |
| [src/lib/server/auth/password.ts](/src/lib/server/auth/password.ts) | TypeScript | 29 | 0 | 6 | 35 |
| [src/lib/server/auth/passwordReset.ts](/src/lib/server/auth/passwordReset.ts) | TypeScript | 89 | 1 | 19 | 109 |
| [src/lib/server/db/helpers.ts](/src/lib/server/db/helpers.ts) | TypeScript | 81 | 0 | 17 | 98 |
| [src/lib/server/db/index.ts](/src/lib/server/db/index.ts) | TypeScript | 23 | 0 | 5 | 28 |
| [src/lib/server/db/schema.ts](/src/lib/server/db/schema.ts) | TypeScript | 158 | 0 | 19 | 177 |
| [src/lib/server/email/email.ts](/src/lib/server/email/email.ts) | TypeScript | 47 | 0 | 9 | 56 |
| [src/lib/server/email/index.ts](/src/lib/server/email/index.ts) | TypeScript | 1 | 0 | 1 | 2 |
| [src/lib/server/index.ts](/src/lib/server/index.ts) | TypeScript | 5 | 0 | 1 | 6 |
| [src/lib/server/pdf/pdfAnalysis.ts](/src/lib/server/pdf/pdfAnalysis.ts) | TypeScript | 238 | 0 | 52 | 290 |
| [src/lib/server/storage/archiveExtractor.ts](/src/lib/server/storage/archiveExtractor.ts) | TypeScript | 333 | 29 | 63 | 425 |
| [src/lib/server/storage/archiveHandler.ts](/src/lib/server/storage/archiveHandler.ts) | TypeScript | 201 | 12 | 52 | 265 |
| [src/lib/server/storage/fileStorage.ts](/src/lib/server/storage/fileStorage.ts) | TypeScript | 44 | 0 | 10 | 54 |
| [src/lib/server/storage/index.ts](/src/lib/server/storage/index.ts) | TypeScript | 2 | 0 | 1 | 3 |
| [src/lib/stores/loading.ts](/src/lib/stores/loading.ts) | TypeScript | 19 | 0 | 4 | 23 |
| [src/lib/stores/locale.ts](/src/lib/stores/locale.ts) | TypeScript | 834 | 68 | 73 | 975 |
| [src/lib/stores/theme.ts](/src/lib/stores/theme.ts) | TypeScript | 24 | 0 | 6 | 30 |
| [src/lib/types.ts](/src/lib/types.ts) | TypeScript | 107 | 5 | 14 | 126 |
| [src/lib/utils/auth.ts](/src/lib/utils/auth.ts) | TypeScript | 39 | 0 | 12 | 51 |
| [src/lib/utils/complexityCalculator.ts](/src/lib/utils/complexityCalculator.ts) | TypeScript | 228 | 0 | 42 | 270 |
| [src/lib/utils/validation.ts](/src/lib/utils/validation.ts) | TypeScript | 75 | 3 | 13 | 91 |
| [src/routes/+layout.server.ts](/src/routes/+layout.server.ts) | TypeScript | 7 | 0 | 1 | 8 |
| [src/routes/+layout.svelte](/src/routes/+layout.svelte) | Svelte | 32 | 0 | 4 | 36 |
| [src/routes/+page.svelte](/src/routes/+page.svelte) | Svelte | 84 | 0 | 7 | 91 |
| [src/routes/api/analyze-pdf-text/+server.ts](/src/routes/api/analyze-pdf-text/+server.ts) | TypeScript | 49 | 0 | 7 | 56 |
| [src/routes/api/analyze-submission/+server.ts](/src/routes/api/analyze-submission/+server.ts) | TypeScript | 150 | 6 | 30 | 186 |
| [src/routes/api/batch-process/+server.ts](/src/routes/api/batch-process/+server.ts) | TypeScript | 77 | 3 | 15 | 95 |
| [src/routes/api/batch-process/download/\[filename\]/+server.ts](/src/routes/api/batch-process/download/%5Bfilename%5D/+server.ts) | TypeScript | 60 | 0 | 10 | 70 |
| [src/routes/api/check-cs-file/+server.ts](/src/routes/api/check-cs-file/+server.ts) | TypeScript | 47 | 2 | 12 | 61 |
| [src/routes/api/download-cs-file/+server.ts](/src/routes/api/download-cs-file/+server.ts) | TypeScript | 39 | 0 | 7 | 46 |
| [src/routes/api/download-selected-cs-files/+server.ts](/src/routes/api/download-selected-cs-files/+server.ts) | TypeScript | 61 | 0 | 13 | 74 |
| [src/routes/api/list-cs-files/+server.ts](/src/routes/api/list-cs-files/+server.ts) | TypeScript | 62 | 1 | 14 | 77 |
| [src/routes/api/pdf/\[...path\]/+server.ts](/src/routes/api/pdf/%5B...path%5D/+server.ts) | TypeScript | 38 | 3 | 8 | 49 |
| [src/routes/api/projects/\[projectId\]/+server.ts](/src/routes/api/projects/%5BprojectId%5D/+server.ts) | TypeScript | 30 | 0 | 6 | 36 |
| [src/routes/api/projects/\[projectId\]/assigned-students/+server.ts](/src/routes/api/projects/%5BprojectId%5D/assigned-students/+server.ts) | TypeScript | 42 | 1 | 5 | 48 |
| [src/routes/api/projects/\[projectId\]/groups/+server.ts](/src/routes/api/projects/%5BprojectId%5D/groups/+server.ts) | TypeScript | 39 | 1 | 5 | 45 |
| [src/routes/api/submissions/\[submissionId\]/+server.ts](/src/routes/api/submissions/%5BsubmissionId%5D/+server.ts) | TypeScript | 54 | 0 | 11 | 65 |
| [src/routes/auth/forgot-password/+page.server.ts](/src/routes/auth/forgot-password/+page.server.ts) | TypeScript | 53 | 0 | 9 | 62 |
| [src/routes/auth/forgot-password/+page.svelte](/src/routes/auth/forgot-password/+page.svelte) | Svelte | 55 | 0 | 7 | 62 |
| [src/routes/auth/login/+page.server.ts](/src/routes/auth/login/+page.server.ts) | TypeScript | 45 | 0 | 12 | 57 |
| [src/routes/auth/login/+page.svelte](/src/routes/auth/login/+page.svelte) | Svelte | 58 | 0 | 7 | 65 |
| [src/routes/auth/logout/+page.server.ts](/src/routes/auth/logout/+page.server.ts) | TypeScript | 12 | 0 | 3 | 15 |
| [src/routes/auth/register/+page.server.ts](/src/routes/auth/register/+page.server.ts) | TypeScript | 130 | 3 | 21 | 154 |
| [src/routes/auth/register/+page.svelte](/src/routes/auth/register/+page.svelte) | Svelte | 135 | 0 | 17 | 152 |
| [src/routes/auth/reset-password/+page.server.ts](/src/routes/auth/reset-password/+page.server.ts) | TypeScript | 118 | 3 | 18 | 139 |
| [src/routes/auth/reset-password/+page.svelte](/src/routes/auth/reset-password/+page.svelte) | Svelte | 102 | 0 | 12 | 114 |
| [src/routes/dashboard/+layout.server.ts](/src/routes/dashboard/+layout.server.ts) | TypeScript | 9 | 0 | 3 | 12 |
| [src/routes/dashboard/+layout.svelte](/src/routes/dashboard/+layout.svelte) | Svelte | 6 | 0 | 1 | 7 |
| [src/routes/dashboard/+page.server.ts](/src/routes/dashboard/+page.server.ts) | TypeScript | 7 | 0 | 1 | 8 |
| [src/routes/dashboard/+page.svelte](/src/routes/dashboard/+page.svelte) | Svelte | 238 | 2 | 23 | 263 |
| [src/routes/dashboard/admin/+page.server.ts](/src/routes/dashboard/admin/+page.server.ts) | TypeScript | 69 | 2 | 7 | 78 |
| [src/routes/dashboard/admin/+page.svelte](/src/routes/dashboard/admin/+page.svelte) | Svelte | 61 | 0 | 7 | 68 |
| [src/routes/dashboard/admin/approvals/+page.server.ts](/src/routes/dashboard/admin/approvals/+page.server.ts) | TypeScript | 135 | 2 | 21 | 158 |
| [src/routes/dashboard/admin/approvals/+page.svelte](/src/routes/dashboard/admin/approvals/+page.svelte) | Svelte | 133 | 0 | 8 | 141 |
| [src/routes/dashboard/admin/users/+page.server.ts](/src/routes/dashboard/admin/users/+page.server.ts) | TypeScript | 176 | 1 | 14 | 191 |
| [src/routes/dashboard/admin/users/+page.svelte](/src/routes/dashboard/admin/users/+page.svelte) | Svelte | 194 | 0 | 17 | 211 |
| [src/routes/dashboard/admin/users/\[userId\]/+page.server.ts](/src/routes/dashboard/admin/users/%5BuserId%5D/+page.server.ts) | TypeScript | 60 | 0 | 11 | 71 |
| [src/routes/dashboard/admin/users/\[userId\]/+page.svelte](/src/routes/dashboard/admin/users/%5BuserId%5D/+page.svelte) | Svelte | 135 | 0 | 16 | 151 |
| [src/routes/dashboard/admin/users/new/+page.server.ts](/src/routes/dashboard/admin/users/new/+page.server.ts) | TypeScript | 100 | 0 | 18 | 118 |
| [src/routes/dashboard/admin/users/new/+page.svelte](/src/routes/dashboard/admin/users/new/+page.svelte) | Svelte | 97 | 0 | 12 | 109 |
| [src/routes/dashboard/developer/+page.server.ts](/src/routes/dashboard/developer/+page.server.ts) | TypeScript | 60 | 1 | 10 | 71 |
| [src/routes/dashboard/developer/+page.svelte](/src/routes/dashboard/developer/+page.svelte) | Svelte | 144 | 0 | 15 | 159 |
| [src/routes/dashboard/developer/admins/+page.server.ts](/src/routes/dashboard/developer/admins/+page.server.ts) | TypeScript | 77 | 2 | 11 | 90 |
| [src/routes/dashboard/developer/admins/+page.svelte](/src/routes/dashboard/developer/admins/+page.svelte) | Svelte | 170 | 0 | 17 | 187 |
| [src/routes/dashboard/developer/admins/\[adminId\]/+page.server.ts](/src/routes/dashboard/developer/admins/%5BadminId%5D/+page.server.ts) | TypeScript | 171 | 0 | 30 | 201 |
| [src/routes/dashboard/developer/admins/\[adminId\]/+page.svelte](/src/routes/dashboard/developer/admins/%5BadminId%5D/+page.svelte) | Svelte | 196 | 0 | 23 | 219 |
| [src/routes/dashboard/developer/admins/new/+page.server.ts](/src/routes/dashboard/developer/admins/new/+page.server.ts) | TypeScript | 126 | 0 | 20 | 146 |
| [src/routes/dashboard/developer/admins/new/+page.svelte](/src/routes/dashboard/developer/admins/new/+page.svelte) | Svelte | 105 | 0 | 11 | 116 |
| [src/routes/dashboard/developer/organizations/+page.server.ts](/src/routes/dashboard/developer/organizations/+page.server.ts) | TypeScript | 76 | 0 | 11 | 87 |
| [src/routes/dashboard/developer/organizations/+page.svelte](/src/routes/dashboard/developer/organizations/+page.svelte) | Svelte | 159 | 0 | 14 | 173 |
| [src/routes/dashboard/developer/organizations/\[organizationId\]/+page.server.ts](/src/routes/dashboard/developer/organizations/%5BorganizationId%5D/+page.server.ts) | TypeScript | 94 | 0 | 18 | 112 |
| [src/routes/dashboard/developer/organizations/\[organizationId\]/+page.svelte](/src/routes/dashboard/developer/organizations/%5BorganizationId%5D/+page.svelte) | Svelte | 179 | 0 | 17 | 196 |
| [src/routes/dashboard/developer/organizations/new/+page.server.ts](/src/routes/dashboard/developer/organizations/new/+page.server.ts) | TypeScript | 53 | 1 | 12 | 66 |
| [src/routes/dashboard/developer/organizations/new/+page.svelte](/src/routes/dashboard/developer/organizations/new/+page.svelte) | Svelte | 82 | 0 | 9 | 91 |
| [src/routes/dashboard/developer/pdf-analysis/+page.server.ts](/src/routes/dashboard/developer/pdf-analysis/+page.server.ts) | TypeScript | 19 | 0 | 6 | 25 |
| [src/routes/dashboard/developer/pdf-analysis/+page.svelte](/src/routes/dashboard/developer/pdf-analysis/+page.svelte) | Svelte | 365 | 6 | 25 | 396 |
| [src/routes/dashboard/developer/system/settings/+page.server.ts](/src/routes/dashboard/developer/system/settings/+page.server.ts) | TypeScript | 197 | 0 | 41 | 238 |
| [src/routes/dashboard/developer/system/settings/+page.svelte](/src/routes/dashboard/developer/system/settings/+page.svelte) | Svelte | 324 | 4 | 33 | 361 |
| [src/routes/dashboard/lecturer/+page.server.ts](/src/routes/dashboard/lecturer/+page.server.ts) | TypeScript | 96 | 4 | 13 | 113 |
| [src/routes/dashboard/lecturer/+page.svelte](/src/routes/dashboard/lecturer/+page.svelte) | Svelte | 144 | 0 | 10 | 154 |
| [src/routes/dashboard/lecturer/groups/+page.server.ts](/src/routes/dashboard/lecturer/groups/+page.server.ts) | TypeScript | 107 | 10 | 19 | 136 |
| [src/routes/dashboard/lecturer/groups/+page.svelte](/src/routes/dashboard/lecturer/groups/+page.svelte) | Svelte | 119 | 0 | 9 | 128 |
| [src/routes/dashboard/lecturer/groups/\[groupId\]/+page.server.ts](/src/routes/dashboard/lecturer/groups/%5BgroupId%5D/+page.server.ts) | TypeScript | 584 | 1 | 102 | 687 |
| [src/routes/dashboard/lecturer/groups/\[groupId\]/+page.svelte](/src/routes/dashboard/lecturer/groups/%5BgroupId%5D/+page.svelte) | Svelte | 530 | 3 | 40 | 573 |
| [src/routes/dashboard/lecturer/groups/new/+page.server.ts](/src/routes/dashboard/lecturer/groups/new/+page.server.ts) | TypeScript | 153 | 0 | 19 | 172 |
| [src/routes/dashboard/lecturer/groups/new/+page.svelte](/src/routes/dashboard/lecturer/groups/new/+page.svelte) | Svelte | 335 | 2 | 35 | 372 |
| [src/routes/dashboard/lecturer/projects/+page.server.ts](/src/routes/dashboard/lecturer/projects/+page.server.ts) | TypeScript | 55 | 5 | 11 | 71 |
| [src/routes/dashboard/lecturer/projects/+page.svelte](/src/routes/dashboard/lecturer/projects/+page.svelte) | Svelte | 126 | 0 | 9 | 135 |
| [src/routes/dashboard/lecturer/projects/\[projectId\]/+page.server.ts](/src/routes/dashboard/lecturer/projects/%5BprojectId%5D/+page.server.ts) | TypeScript | 477 | 26 | 77 | 580 |
| [src/routes/dashboard/lecturer/projects/\[projectId\]/+page.svelte](/src/routes/dashboard/lecturer/projects/%5BprojectId%5D/+page.svelte) | Svelte | 837 | 2 | 99 | 938 |
| [src/routes/dashboard/lecturer/projects/new/+page.server.ts](/src/routes/dashboard/lecturer/projects/new/+page.server.ts) | TypeScript | 148 | 2 | 21 | 171 |
| [src/routes/dashboard/lecturer/projects/new/+page.svelte](/src/routes/dashboard/lecturer/projects/new/+page.svelte) | Svelte | 272 | 0 | 30 | 302 |
| [src/routes/dashboard/student/+page.server.ts](/src/routes/dashboard/student/+page.server.ts) | TypeScript | 80 | 0 | 7 | 87 |
| [src/routes/dashboard/student/+page.svelte](/src/routes/dashboard/student/+page.svelte) | Svelte | 121 | 0 | 7 | 128 |
| [src/routes/dashboard/student/groups/+page.server.ts](/src/routes/dashboard/student/groups/+page.server.ts) | TypeScript | 146 | 14 | 21 | 181 |
| [src/routes/dashboard/student/groups/+page.svelte](/src/routes/dashboard/student/groups/+page.svelte) | Svelte | 135 | 0 | 10 | 145 |
| [src/routes/dashboard/student/groups/join/+page.server.ts](/src/routes/dashboard/student/groups/join/+page.server.ts) | TypeScript | 69 | 0 | 15 | 84 |
| [src/routes/dashboard/student/groups/join/+page.svelte](/src/routes/dashboard/student/groups/join/+page.svelte) | Svelte | 60 | 0 | 11 | 71 |
| [src/routes/dashboard/student/projects/\[projectId\]/+page.server.ts](/src/routes/dashboard/student/projects/%5BprojectId%5D/+page.server.ts) | TypeScript | 510 | 12 | 90 | 612 |
| [src/routes/dashboard/student/projects/\[projectId\]/+page.svelte](/src/routes/dashboard/student/projects/%5BprojectId%5D/+page.svelte) | Svelte | 588 | 0 | 60 | 648 |
| [src/routes/dashboard/student/submissions/+page.server.ts](/src/routes/dashboard/student/submissions/+page.server.ts) | TypeScript | 186 | 3 | 36 | 225 |
| [src/routes/dashboard/student/submissions/+page.svelte](/src/routes/dashboard/student/submissions/+page.svelte) | Svelte | 127 | 0 | 11 | 138 |
| [src/routes/dashboard/student/submit/+page.server.ts](/src/routes/dashboard/student/submit/+page.server.ts) | TypeScript | 199 | 5 | 38 | 242 |
| [src/routes/dashboard/student/submit/+page.svelte](/src/routes/dashboard/student/submit/+page.svelte) | Svelte | 96 | 0 | 7 | 103 |
| [src/routes/debug/+page.server.ts](/src/routes/debug/+page.server.ts) | TypeScript | 13 | 0 | 2 | 15 |
| [src/routes/debug/+page.svelte](/src/routes/debug/+page.svelte) | Svelte | 32 | 0 | 9 | 41 |
| [src/routes/pending-approval/+page.server.ts](/src/routes/pending-approval/+page.server.ts) | TypeScript | 21 | 1 | 5 | 27 |
| [src/routes/pending-approval/+page.svelte](/src/routes/pending-approval/+page.svelte) | Svelte | 32 | 0 | 4 | 36 |
| [src/routes/settings/+page.server.ts](/src/routes/settings/+page.server.ts) | TypeScript | 149 | 1 | 29 | 179 |
| [src/routes/settings/+page.svelte](/src/routes/settings/+page.svelte) | Svelte | 193 | 7 | 26 | 226 |
| [src/variables.scss](/src/variables.scss) | SCSS | 8 | 1 | 0 | 9 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)