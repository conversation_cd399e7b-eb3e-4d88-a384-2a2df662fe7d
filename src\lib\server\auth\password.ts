import * as argon2 from '@node-rs/argon2';
import crypto from 'crypto';

const ARGON2ID = 2;

// Enhanced Argon2 configuration for better security
const CONFIG = {
  memoryCost: parseInt(process.env.ARGON2_MEMORY_COST || '') || 65536, // Increased from 19456 to 64MB
  timeCost: parseInt(process.env.ARGON2_TIME_COST || '') || 3, // Increased from 2 to 3 iterations
  outputLen: parseInt(process.env.ARGON2_OUTPUT_LENGTH || '') || 32,
  parallelism: parseInt(process.env.ARGON2_PARALLELISM || '') || 1,
  saltLength: parseInt(process.env.ARGON2_SALT_LENGTH || '') || 32 // Increased from 16 to 32 bytes
};

// Minimum delay for password verification to prevent timing attacks
const MIN_VERIFICATION_TIME_MS = 100;

export async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(CONFIG.saltLength);

  return argon2.hash(password, {
    memoryCost: CONFIG.memoryCost,
    timeCost: CONFIG.timeCost,
    parallelism: CONFIG.parallelism,
    outputLen: CONFIG.outputLen,
    salt: salt,
    algorithm: ARGON2ID
  });
}

/**
 * Verifies password with timing attack protection
 */
export async function verifyPassword(hash: string, password: string): Promise<boolean> {
  const startTime = Date.now();

  try {
    // Perform password verification
    const isValid = await argon2.verify(hash, password);

    // Ensure minimum verification time to prevent timing attacks
    const elapsedTime = Date.now() - startTime;
    if (elapsedTime < MIN_VERIFICATION_TIME_MS) {
      await new Promise(resolve => setTimeout(resolve, MIN_VERIFICATION_TIME_MS - elapsedTime));
    }

    return isValid;
  } catch (error) {
    console.error('Password verification error:', error);

    // Ensure consistent timing even on error
    const elapsedTime = Date.now() - startTime;
    if (elapsedTime < MIN_VERIFICATION_TIME_MS) {
      await new Promise(resolve => setTimeout(resolve, MIN_VERIFICATION_TIME_MS - elapsedTime));
    }

    return false;
  }
}

/**
 * Validates password strength according to security policy
 */
export function validatePasswordStrength(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (password.length > 255) {
    errors.push('Password must not exceed 255 characters');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[^a-zA-Z0-9]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  // Check for common patterns
  const commonPatterns = [
    /(.)\1{2,}/, // Repeated characters (aaa, 111, etc.)
    /123|234|345|456|567|678|789|890/, // Sequential numbers
    /abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz/i, // Sequential letters
    /password|123456|qwerty|admin|login|user/i // Common weak passwords
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      errors.push('Password contains common patterns and is too predictable');
      break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
