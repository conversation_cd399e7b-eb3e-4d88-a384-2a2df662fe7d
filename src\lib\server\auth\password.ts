import * as argon2 from '@node-rs/argon2';
import crypto from 'crypto';

const ARGON2ID = 2;

const CONFIG = {
  memoryCost: parseInt(process.env.ARGON2_MEMORY_COST || '') || 19456,
  timeCost: parseInt(process.env.ARGON2_TIME_COST || '') || 2,
  outputLen: parseInt(process.env.ARGON2_OUTPUT_LENGTH || '') || 32,
  parallelism: parseInt(process.env.ARGON2_PARALLELISM || '') || 1,
  saltLength: parseInt(process.env.ARGON2_SALT_LENGTH || '') || 16
};

export async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(CONFIG.saltLength);

  return argon2.hash(password, {
    memoryCost: CONFIG.memoryCost,
    timeCost: CONFIG.timeCost,
    parallelism: CONFIG.parallelism,
    outputLen: CONFIG.outputLen,
    salt: salt,
    algorithm: ARGON2ID
  });
}

export async function verifyPassword(hash: string, password: string): Promise<boolean> {
  try {
    return await argon2.verify(hash, password);
  } catch (error) {
    console.error('Password verification error:', error);
    return false;
  }
}
