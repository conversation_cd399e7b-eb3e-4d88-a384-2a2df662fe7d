import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { eq, and, gte, desc } from 'drizzle-orm';
import crypto from 'crypto';

// Account lockout configuration
const LOCKOUT_CONFIG = {
  MAX_FAILED_ATTEMPTS: 5,
  LOCKOUT_DURATION_MINUTES: 30,
  ATTEMPT_WINDOW_MINUTES: 15,
  PROGRESSIVE_LOCKOUT: true,
  MAX_LOCKOUT_DURATION_HOURS: 24
};

export interface LoginAttemptResult {
  success: boolean;
  isLocked: boolean;
  lockoutUntil?: Date;
  remainingAttempts?: number;
  message?: string;
}

/**
 * Records a login attempt in the database
 */
export async function recordLoginAttempt(
  username: string,
  ipAddress: string,
  userAgent: string | null,
  success: boolean,
  failureReason?: string
): Promise<void> {
  try {
    await db.insert(table.loginAttempt).values({
      id: crypto.randomUUID(),
      username,
      ipAddress,
      userAgent,
      success,
      failureReason,
      attemptedAt: new Date()
    });
  } catch (error) {
    console.error('Error recording login attempt:', error);
  }
}

/**
 * Checks if an account is currently locked
 */
export async function isAccountLocked(userId: string): Promise<{ isLocked: boolean; lockoutUntil?: Date }> {
  try {
    const [activeLockout] = await db
      .select()
      .from(table.accountLockout)
      .where(
        and(
          eq(table.accountLockout.userId, userId),
          eq(table.accountLockout.isActive, true),
          gte(table.accountLockout.lockedUntil, new Date())
        )
      )
      .orderBy(desc(table.accountLockout.lockedAt))
      .limit(1);

    if (activeLockout) {
      return {
        isLocked: true,
        lockoutUntil: activeLockout.lockedUntil
      };
    }

    return { isLocked: false };
  } catch (error) {
    console.error('Error checking account lockout:', error);
    return { isLocked: false };
  }
}

/**
 * Gets recent failed login attempts for a user
 */
export async function getRecentFailedAttempts(username: string, windowMinutes: number = LOCKOUT_CONFIG.ATTEMPT_WINDOW_MINUTES): Promise<number> {
  try {
    const windowStart = new Date(Date.now() - windowMinutes * 60 * 1000);
    
    const attempts = await db
      .select()
      .from(table.loginAttempt)
      .where(
        and(
          eq(table.loginAttempt.username, username),
          eq(table.loginAttempt.success, false),
          gte(table.loginAttempt.attemptedAt, windowStart)
        )
      );

    return attempts.length;
  } catch (error) {
    console.error('Error getting recent failed attempts:', error);
    return 0;
  }
}

/**
 * Locks an account after too many failed attempts
 */
export async function lockAccount(userId: string, failedAttempts: number, reason: string): Promise<Date> {
  try {
    // Calculate lockout duration (progressive lockout)
    let lockoutMinutes = LOCKOUT_CONFIG.LOCKOUT_DURATION_MINUTES;
    
    if (LOCKOUT_CONFIG.PROGRESSIVE_LOCKOUT) {
      // Progressive lockout: 30min, 1hr, 2hr, 4hr, 8hr, 24hr max
      const multiplier = Math.min(Math.pow(2, failedAttempts - LOCKOUT_CONFIG.MAX_FAILED_ATTEMPTS), 48);
      lockoutMinutes = Math.min(
        LOCKOUT_CONFIG.LOCKOUT_DURATION_MINUTES * multiplier,
        LOCKOUT_CONFIG.MAX_LOCKOUT_DURATION_HOURS * 60
      );
    }

    const lockedUntil = new Date(Date.now() + lockoutMinutes * 60 * 1000);

    // Deactivate any existing lockouts
    await db
      .update(table.accountLockout)
      .set({ isActive: false })
      .where(eq(table.accountLockout.userId, userId));

    // Create new lockout record
    await db.insert(table.accountLockout).values({
      id: crypto.randomUUID(),
      userId,
      lockedAt: new Date(),
      lockedUntil,
      failedAttempts,
      lockReason: reason,
      isActive: true
    });

    console.log(`Account locked for user ${userId} until ${lockedUntil.toISOString()}`);
    return lockedUntil;
  } catch (error) {
    console.error('Error locking account:', error);
    throw error;
  }
}

/**
 * Unlocks an account manually (admin action)
 */
export async function unlockAccount(userId: string): Promise<void> {
  try {
    await db
      .update(table.accountLockout)
      .set({ isActive: false })
      .where(
        and(
          eq(table.accountLockout.userId, userId),
          eq(table.accountLockout.isActive, true)
        )
      );

    console.log(`Account manually unlocked for user ${userId}`);
  } catch (error) {
    console.error('Error unlocking account:', error);
    throw error;
  }
}

/**
 * Clears failed login attempts after successful login
 */
export async function clearFailedAttempts(username: string): Promise<void> {
  try {
    // We don't delete the records for audit purposes, but we could mark them as cleared
    // For now, the natural expiration based on time window is sufficient
    console.log(`Cleared failed attempts tracking for user ${username}`);
  } catch (error) {
    console.error('Error clearing failed attempts:', error);
  }
}

/**
 * Validates login attempt and handles lockout logic
 */
export async function validateLoginAttempt(
  username: string,
  userId: string | null,
  ipAddress: string,
  userAgent: string | null,
  success: boolean,
  failureReason?: string
): Promise<LoginAttemptResult> {
  try {
    // Record the attempt
    await recordLoginAttempt(username, ipAddress, userAgent, success, failureReason);

    // If login was successful, clear tracking and return success
    if (success && userId) {
      await clearFailedAttempts(username);
      return { success: true, isLocked: false };
    }

    // Check if account is already locked
    if (userId) {
      const lockStatus = await isAccountLocked(userId);
      if (lockStatus.isLocked) {
        return {
          success: false,
          isLocked: true,
          lockoutUntil: lockStatus.lockoutUntil,
          message: `Account is locked until ${lockStatus.lockoutUntil?.toLocaleString()}`
        };
      }
    }

    // Check recent failed attempts
    const recentFailures = await getRecentFailedAttempts(username);
    const remainingAttempts = LOCKOUT_CONFIG.MAX_FAILED_ATTEMPTS - recentFailures;

    // If we've exceeded the limit, lock the account
    if (recentFailures >= LOCKOUT_CONFIG.MAX_FAILED_ATTEMPTS && userId) {
      const lockoutUntil = await lockAccount(
        userId,
        recentFailures,
        `Too many failed login attempts (${recentFailures})`
      );

      return {
        success: false,
        isLocked: true,
        lockoutUntil,
        message: `Account locked due to too many failed attempts. Try again after ${lockoutUntil.toLocaleString()}`
      };
    }

    // Return failure with remaining attempts
    return {
      success: false,
      isLocked: false,
      remainingAttempts: Math.max(0, remainingAttempts),
      message: remainingAttempts > 0 
        ? `Invalid credentials. ${remainingAttempts} attempts remaining.`
        : 'Invalid credentials.'
    };
  } catch (error) {
    console.error('Error validating login attempt:', error);
    return {
      success: false,
      isLocked: false,
      message: 'An error occurred during login validation'
    };
  }
}

/**
 * Gets lockout statistics for monitoring
 */
export async function getLockoutStats(): Promise<{
  totalLockouts: number;
  activeLockouts: number;
  recentAttempts: number;
}> {
  try {
    const [totalLockouts] = await db
      .select({ count: table.accountLockout.id })
      .from(table.accountLockout);

    const [activeLockouts] = await db
      .select({ count: table.accountLockout.id })
      .from(table.accountLockout)
      .where(
        and(
          eq(table.accountLockout.isActive, true),
          gte(table.accountLockout.lockedUntil, new Date())
        )
      );

    const recentWindow = new Date(Date.now() - 24 * 60 * 60 * 1000); // Last 24 hours
    const [recentAttempts] = await db
      .select({ count: table.loginAttempt.id })
      .from(table.loginAttempt)
      .where(gte(table.loginAttempt.attemptedAt, recentWindow));

    return {
      totalLockouts: totalLockouts?.count || 0,
      activeLockouts: activeLockouts?.count || 0,
      recentAttempts: recentAttempts?.count || 0
    };
  } catch (error) {
    console.error('Error getting lockout stats:', error);
    return { totalLockouts: 0, activeLockouts: 0, recentAttempts: 0 };
  }
}
