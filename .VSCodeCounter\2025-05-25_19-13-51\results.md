# Summary

Date : 2025-05-25 19:13:51

Directory c:\\pdf-bak\\src

Total : 158 files,  19320 codes, 334 comments, 2633 blanks, all 22287 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Svelte | 73 | 9,660 | 34 | 1,012 | 10,706 |
| TypeScript | 82 | 8,858 | 262 | 1,522 | 10,642 |
| PostCSS | 1 | 764 | 37 | 97 | 898 |
| HTML | 1 | 30 | 0 | 2 | 32 |
| SCSS | 1 | 8 | 1 | 0 | 9 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 158 | 19,320 | 334 | 2,633 | 22,287 |
| . (Files) | 5 | 884 | 53 | 119 | 1,056 |
| lib | 64 | 6,100 | 139 | 889 | 7,128 |
| lib (Files) | 3 | 188 | 8 | 31 | 227 |
| lib\\actions | 1 | 14 | 0 | 4 | 18 |
| lib\\components | 36 | 2,893 | 8 | 341 | 3,242 |
| lib\\components\\ui | 36 | 2,893 | 8 | 341 | 3,242 |
| lib\\components\\ui (Files) | 1 | 34 | 0 | 8 | 42 |
| lib\\components\\ui\\button | 1 | 57 | 0 | 4 | 61 |
| lib\\components\\ui\\data | 7 | 235 | 0 | 19 | 254 |
| lib\\components\\ui\\feedback | 5 | 287 | 0 | 51 | 338 |
| lib\\components\\ui\\input | 4 | 519 | 0 | 52 | 571 |
| lib\\components\\ui\\layout | 6 | 628 | 5 | 60 | 693 |
| lib\\components\\ui\\navigation | 4 | 170 | 0 | 21 | 191 |
| lib\\components\\ui\\overlay | 5 | 800 | 3 | 104 | 907 |
| lib\\components\\ui\\utility | 3 | 163 | 0 | 22 | 185 |
| lib\\server | 18 | 1,786 | 52 | 363 | 2,201 |
| lib\\server (Files) | 1 | 5 | 0 | 1 | 6 |
| lib\\server\\api | 3 | 433 | 9 | 87 | 529 |
| lib\\server\\auth | 4 | 220 | 2 | 46 | 268 |
| lib\\server\\db | 3 | 262 | 0 | 41 | 303 |
| lib\\server\\email | 2 | 48 | 0 | 10 | 58 |
| lib\\server\\pdf | 1 | 238 | 0 | 52 | 290 |
| lib\\server\\storage | 4 | 580 | 41 | 126 | 747 |
| lib\\stores | 3 | 877 | 68 | 83 | 1,028 |
| lib\\utils | 3 | 342 | 3 | 67 | 412 |
| routes | 89 | 12,336 | 142 | 1,625 | 14,103 |
| routes (Files) | 3 | 123 | 0 | 12 | 135 |
| routes\\api | 13 | 748 | 17 | 143 | 908 |
| routes\\api\\analyze-pdf-text | 1 | 49 | 0 | 7 | 56 |
| routes\\api\\analyze-submission | 1 | 150 | 6 | 30 | 186 |
| routes\\api\\batch-process | 2 | 137 | 3 | 25 | 165 |
| routes\\api\\batch-process (Files) | 1 | 77 | 3 | 15 | 95 |
| routes\\api\\batch-process\\download | 1 | 60 | 0 | 10 | 70 |
| routes\\api\\batch-process\\download\\[filename] | 1 | 60 | 0 | 10 | 70 |
| routes\\api\\check-cs-file | 1 | 47 | 2 | 12 | 61 |
| routes\\api\\download-cs-file | 1 | 39 | 0 | 7 | 46 |
| routes\\api\\download-selected-cs-files | 1 | 61 | 0 | 13 | 74 |
| routes\\api\\list-cs-files | 1 | 62 | 1 | 14 | 77 |
| routes\\api\\pdf | 1 | 38 | 3 | 8 | 49 |
| routes\\api\\pdf\\[...path] | 1 | 38 | 3 | 8 | 49 |
| routes\\api\\projects | 3 | 111 | 2 | 16 | 129 |
| routes\\api\\projects\\[projectId] | 3 | 111 | 2 | 16 | 129 |
| routes\\api\\projects\\[projectId] (Files) | 1 | 30 | 0 | 6 | 36 |
| routes\\api\\projects\\[projectId]\\assigned-students | 1 | 42 | 1 | 5 | 48 |
| routes\\api\\projects\\[projectId]\\groups | 1 | 39 | 1 | 5 | 45 |
| routes\\api\\submissions | 1 | 54 | 0 | 11 | 65 |
| routes\\api\\submissions\\[submissionId] | 1 | 54 | 0 | 11 | 65 |
| routes\\auth | 9 | 708 | 6 | 106 | 820 |
| routes\\auth\\forgot-password | 2 | 108 | 0 | 16 | 124 |
| routes\\auth\\login | 2 | 103 | 0 | 19 | 122 |
| routes\\auth\\logout | 1 | 12 | 0 | 3 | 15 |
| routes\\auth\\register | 2 | 265 | 3 | 38 | 306 |
| routes\\auth\\reset-password | 2 | 220 | 3 | 30 | 253 |
| routes\\dashboard | 58 | 10,317 | 110 | 1,289 | 11,716 |
| routes\\dashboard (Files) | 4 | 260 | 2 | 28 | 290 |
| routes\\dashboard\\admin | 10 | 1,160 | 5 | 131 | 1,296 |
| routes\\dashboard\\admin (Files) | 2 | 130 | 2 | 14 | 146 |
| routes\\dashboard\\admin\\approvals | 2 | 268 | 2 | 29 | 299 |
| routes\\dashboard\\admin\\users | 6 | 762 | 1 | 88 | 851 |
| routes\\dashboard\\admin\\users (Files) | 2 | 370 | 1 | 31 | 402 |
| routes\\dashboard\\admin\\users\\[userId] | 2 | 195 | 0 | 27 | 222 |
| routes\\dashboard\\admin\\users\\new | 2 | 197 | 0 | 30 | 227 |
| routes\\dashboard\\developer | 18 | 2,597 | 14 | 323 | 2,934 |
| routes\\dashboard\\developer (Files) | 2 | 204 | 1 | 25 | 230 |
| routes\\dashboard\\developer\\admins | 6 | 845 | 2 | 112 | 959 |
| routes\\dashboard\\developer\\admins (Files) | 2 | 247 | 2 | 28 | 277 |
| routes\\dashboard\\developer\\admins\\[adminId] | 2 | 367 | 0 | 53 | 420 |
| routes\\dashboard\\developer\\admins\\new | 2 | 231 | 0 | 31 | 262 |
| routes\\dashboard\\developer\\organizations | 6 | 643 | 1 | 81 | 725 |
| routes\\dashboard\\developer\\organizations (Files) | 2 | 235 | 0 | 25 | 260 |
| routes\\dashboard\\developer\\organizations\\[organizationId] | 2 | 273 | 0 | 35 | 308 |
| routes\\dashboard\\developer\\organizations\\new | 2 | 135 | 1 | 21 | 157 |
| routes\\dashboard\\developer\\pdf-analysis | 2 | 384 | 6 | 31 | 421 |
| routes\\dashboard\\developer\\system | 2 | 521 | 4 | 74 | 599 |
| routes\\dashboard\\developer\\system\\settings | 2 | 521 | 4 | 74 | 599 |
| routes\\dashboard\\lecturer | 14 | 3,983 | 55 | 494 | 4,532 |
| routes\\dashboard\\lecturer (Files) | 2 | 240 | 4 | 23 | 267 |
| routes\\dashboard\\lecturer\\groups | 6 | 1,828 | 16 | 224 | 2,068 |
| routes\\dashboard\\lecturer\\groups (Files) | 2 | 226 | 10 | 28 | 264 |
| routes\\dashboard\\lecturer\\groups\\[groupId] | 2 | 1,114 | 4 | 142 | 1,260 |
| routes\\dashboard\\lecturer\\groups\\new | 2 | 488 | 2 | 54 | 544 |
| routes\\dashboard\\lecturer\\projects | 6 | 1,915 | 35 | 247 | 2,197 |
| routes\\dashboard\\lecturer\\projects (Files) | 2 | 181 | 5 | 20 | 206 |
| routes\\dashboard\\lecturer\\projects\\[projectId] | 2 | 1,314 | 28 | 176 | 1,518 |
| routes\\dashboard\\lecturer\\projects\\new | 2 | 420 | 2 | 51 | 473 |
| routes\\dashboard\\student | 12 | 2,317 | 34 | 313 | 2,664 |
| routes\\dashboard\\student (Files) | 2 | 201 | 0 | 14 | 215 |
| routes\\dashboard\\student\\groups | 4 | 410 | 14 | 57 | 481 |
| routes\\dashboard\\student\\groups (Files) | 2 | 281 | 14 | 31 | 326 |
| routes\\dashboard\\student\\groups\\join | 2 | 129 | 0 | 26 | 155 |
| routes\\dashboard\\student\\projects | 2 | 1,098 | 12 | 150 | 1,260 |
| routes\\dashboard\\student\\projects\\[projectId] | 2 | 1,098 | 12 | 150 | 1,260 |
| routes\\dashboard\\student\\submissions | 2 | 313 | 3 | 47 | 363 |
| routes\\dashboard\\student\\submit | 2 | 295 | 5 | 45 | 345 |
| routes\\debug | 2 | 45 | 0 | 11 | 56 |
| routes\\pending-approval | 2 | 53 | 1 | 9 | 63 |
| routes\\settings | 2 | 342 | 8 | 55 | 405 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)