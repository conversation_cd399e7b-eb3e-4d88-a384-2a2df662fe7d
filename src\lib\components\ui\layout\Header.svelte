<script lang="ts">
  import { page } from '$app/stores';
  import { enhance } from '$app/forms';
  import { fly, slide } from 'svelte/transition';
  import { onMount } from 'svelte';
  import { locale, t, type Locale } from '$lib/stores/locale';
  import { theme, type ThemeMode } from '$lib/stores/theme';
  import { ThemeSwitcher } from '$lib/components/ui';
  import { Button, NavLink } from '$lib/components/ui';

  let mobileMenuOpen = $state(false);
  let userDropdownOpen = $state(false);
  let currentLocale = $state<Locale>($locale);
  let currentTheme = $state<ThemeMode>($theme);

  $effect(() => {
    currentLocale = $locale;
    currentTheme = $theme;
  });

  function toggleMobileMenu() {
    mobileMenuOpen = !mobileMenuOpen;
  }

  function toggleUserDropdown() {
    userDropdownOpen = !userDropdownOpen;
  }

  onMount(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById("user-dropdown");
      const userButton = document.getElementById("user-dropdown-button");

      if (dropdown && userDropdownOpen &&
          !dropdown.contains(event.target as Node) &&
          !userButton?.contains(event.target as Node)) {
        userDropdownOpen = false;
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  });
</script>

<style lang="tailwind">
  .user-menu-container {
    height: 4rem;
    display: flex;
    align-items: center;
  }

  .dropdown-wrapper {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    width: 12rem;
    z-index: 50;
  }

  .mobile-menu {
    position: absolute;
    left: 0;
    right: 0;
    background-color: white;
    z-index: 40;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    width: 100%;
  }

  :global(.dark) .mobile-menu {
    background-color: #1f2937;
    border-bottom-color: #374151;
  }
</style>

{#if $page.data?.user}
  <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 w-full">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
      <div class="flex justify-between h-16 w-full">
        <div class="flex items-center">
          <a href="/dashboard" class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-indigo-600 dark:text-indigo-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
            </svg>
            <span class="ml-2 text-gray-900 dark:text-white font-semibold text-xl">PDFBankas</span>
          </a>
        </div>

        <nav class="hidden md:flex items-center space-x-6">
          {#if $page.data.user?.role === 'student'}
            <NavLink
              href="/dashboard/student"
              isActive={$page.url.pathname.startsWith('/dashboard/student') && !$page.url.pathname.includes('/submissions') && !$page.url.pathname.includes('/groups') && !$page.url.pathname.includes('/submit')}
              label={t('header.dashboard', currentLocale)}
            />
            <NavLink
              href="/dashboard/student/groups"
              isActive={$page.url.pathname.includes('/groups')}
              label={t('header.groups', currentLocale)}
            />
            <NavLink
              href="/dashboard/student/submit"
              isActive={$page.url.pathname.includes('/submit')}
              label={t('header.submit', currentLocale)}
            />
            <NavLink
              href="/dashboard/student/submissions"
              isActive={$page.url.pathname.includes('/submissions')}
              label={t('header.submissions', currentLocale)}
            />
          {:else if $page.data.user?.role === 'lecturer'}
            <NavLink
              href="/dashboard/lecturer"
              isActive={$page.url.pathname === '/dashboard/lecturer'}
              label={t('header.dashboard', currentLocale)}
            />
            <NavLink
              href="/dashboard/lecturer/groups"
              isActive={$page.url.pathname.includes('/groups')}
              label={t('header.groups', currentLocale)}
            />
            <NavLink
              href="/dashboard/lecturer/projects"
              isActive={$page.url.pathname.includes('/projects')}
              label={t('header.projects', currentLocale)}
            />

          {:else if $page.data.user?.role === 'admin'}
            <NavLink
              href="/dashboard/admin"
              isActive={$page.url.pathname === '/dashboard/admin'}
              label={t('header.dashboard', currentLocale)}
            />
            <NavLink
              href="/dashboard/admin/users"
              isActive={$page.url.pathname.includes('/users')}
              label={t('header.users', currentLocale)}
            />
            <NavLink
              href="/dashboard/admin/approvals"
              isActive={$page.url.pathname.includes('/approvals')}
              label={t('header.approvals', currentLocale)}
            />
          {:else if $page.data.user?.role === 'developer'}
            <NavLink
              href="/dashboard/developer"
              isActive={$page.url.pathname === '/dashboard/developer'}
              label={t('header.dashboard', currentLocale)}
            />
            <NavLink
              href="/dashboard/developer/admins"
              isActive={$page.url.pathname.includes('/admins')}
              label={t('header.adminManagement', currentLocale)}
            />
            <NavLink
              href="/dashboard/developer/system/settings"
              isActive={$page.url.pathname.includes('/system')}
              label={t('header.system', currentLocale)}
            />
          {/if}
        </nav>

        <div class="flex items-center space-x-4">
          <div class="hidden md:block relative user-menu-container">
            <div class="flex items-center h-full">
              <button
                id="user-dropdown-button"
                type="button"
                onclick={(e) => { e.stopPropagation(); toggleUserDropdown(); }}
                class="flex items-center space-x-2 text-sm bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700 px-3 py-1.5 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 focus:ring-offset-2 transition-colors duration-200 h-10"
              >
                <div class="h-6 w-6 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex items-center justify-center flex-shrink-0">
                  {$page.data.user?.username?.charAt(0)?.toUpperCase() || 'U'}
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-200 max-w-[120px] truncate overflow-hidden">{$page.data.user?.username || t('common.user', currentLocale)}</span>
                <svg class="h-4 w-4 text-gray-500 dark:text-gray-400 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>

              <div class="dropdown-wrapper">
                {#if userDropdownOpen}
                  <div
                    id="user-dropdown"
                    transition:fly|local={{ y: -5, duration: 150 }}
                    class="absolute right-0 z-10 mt-2 w-56 origin-top-right bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 border border-gray-200 dark:border-gray-700 transition-colors duration-200"
                  >
                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                      <p class="text-sm font-medium text-gray-900 dark:text-white">{$page.data.user?.username || t('common.user', currentLocale)}</p>
                      <p class="text-xs text-gray-500 dark:text-gray-400 capitalize">{$page.data.user?.role || t('common.user', currentLocale).toLowerCase()}</p>
                    </div>
                    <a href="/settings" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                      {t('common.settings', currentLocale)}
                    </a>
                    {#if $page.data.user?.role === 'developer'}
                      <a href="/docs/components" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        Component Docs
                      </a>
                    {/if}

                    <!-- Language selector -->
                    <div class="border-t border-gray-200 dark:border-gray-700 mt-1 pt-1">
                      <div class="px-4 py-2">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">{t('language.select', currentLocale)}</p>
                        <div class="flex items-center space-x-2">
                          <button
                            onclick={() => locale.set('en')}
                            class="bg-blue-600 text-white px-3 py-1 rounded-md flex items-center text-xs {currentLocale === 'en' ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}"
                            disabled={currentLocale === 'en'}
                          >
                            <span class="mr-1">🇬🇧</span> {t('language.english', currentLocale)}
                          </button>
                          <button
                            onclick={() => locale.set('lt')}
                            class="bg-blue-600 text-white px-3 py-1 rounded-md flex items-center text-xs {currentLocale === 'lt' ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}"
                            disabled={currentLocale === 'lt'}
                          >
                            <span class="mr-1">🇱🇹</span> {t('language.lithuanian', currentLocale)}
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Theme selector -->
                    <div class="border-t border-gray-200 dark:border-gray-700 mt-1 pt-1">
                      <div class="px-4 py-2">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">{t('theme.select', currentLocale)}</p>
                        <div class="flex flex-col space-y-1">
                          <button
                            onclick={() => theme.set('light')}
                            class="bg-blue-600 text-white px-3 py-1 rounded-md flex items-center text-sm w-full justify-start {currentTheme === 'light' ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}"
                            disabled={currentTheme === 'light'}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
                            </svg>
                            {t('theme.light', currentLocale)}
                          </button>
                          <button
                            onclick={() => theme.set('dark')}
                            class="bg-blue-600 text-white px-3 py-1 rounded-md flex items-center text-sm w-full justify-start {currentTheme === 'dark' ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}"
                            disabled={currentTheme === 'dark'}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                            </svg>
                            {t('theme.dark', currentLocale)}
                          </button>
                        </div>
                      </div>
                    </div>

                    <div class="border-t border-gray-200 dark:border-gray-700 mt-1"></div>
                    <form action="/auth/logout" method="POST" class="block" use:enhance>
                      <Button
                        type="submit"
                        variant="danger"
                        size="md"
                        class="w-full justify-center py-2 mt-2"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        <span class="font-medium">{t('common.signOut', currentLocale)}</span>
                      </Button>
                    </form>
                  </div>
                {/if}
              </div>
            </div>
          </div>

          <!-- Theme Switcher -->
          <div class="hidden md:flex items-center h-16">
            <div class="mr-2">
              <ThemeSwitcher />
            </div>
          </div>

          <!-- Mobile button -->
          <div class="flex items-center h-16 md:hidden">
            <Button
              onClick={toggleMobileMenu}
              variant="light"
              size="sm"
              class="p-2"
            >
              <span class="sr-only">Open main menu</span>
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </Button>
          </div>
        </div>
      </div>
    </div>

    {#if mobileMenuOpen}
      <div
        transition:slide|local={{ duration: 200 }}
        class="mobile-menu md:hidden"
      >
        <div class="px-2 pt-2 pb-3 space-y-1">
          {#if $page.data.user?.role === 'student'}
            <a href="/dashboard/student" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname === '/dashboard/student' ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.dashboard', currentLocale)}</a>
            <a href="/dashboard/student/groups" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/groups') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.groups', currentLocale)}</a>
            <a href="/dashboard/student/submit" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/submit') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.submit', currentLocale)}</a>
            <a href="/dashboard/student/submissions" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/submissions') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.submissions', currentLocale)}</a>
          {:else if $page.data.user?.role === 'lecturer'}
            <a href="/dashboard/lecturer" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname === '/dashboard/lecturer' ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.dashboard', currentLocale)}</a>
            <a href="/dashboard/lecturer/groups" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/groups') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.groups', currentLocale)}</a>
            <a href="/dashboard/lecturer/projects" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/projects') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.projects', currentLocale)}</a>

          {:else if $page.data.user?.role === 'admin'}
            <a href="/dashboard/admin" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname === '/dashboard/admin' ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.dashboard', currentLocale)}</a>
            <a href="/dashboard/admin/users" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/users') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.users', currentLocale)}</a>
            <a href="/dashboard/admin/projects" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/projects') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.projects', currentLocale)}</a>

            <a href="/dashboard/admin/approvals" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/approvals') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.approvals', currentLocale)}</a>
          {:else if $page.data.user?.role === 'developer'}
            <a href="/dashboard/developer" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname === '/dashboard/developer' ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.dashboard', currentLocale)}</a>

            <a href="/dashboard/developer/admins" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/admins') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.adminManagement', currentLocale)}</a>
            <a href="/dashboard/developer/system/settings" class="block px-3 py-2 rounded-md text-base font-medium {$page.url.pathname.includes('/system/settings') ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 hover:text-gray-900 dark:hover:bg-gray-700 dark:hover:text-white'}">{t('header.system', currentLocale)}</a>
          {/if}
        </div>

        <div class="pt-4 pb-3 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center px-4">
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-300 flex items-center justify-center">
                {$page.data.user?.username?.charAt(0)?.toUpperCase() || 'U'}
              </div>
            </div>
            <div class="ml-3">
              <div class="text-base font-medium text-gray-800 dark:text-white">{$page.data.user?.username || t('common.user', currentLocale)}</div>
              <div class="text-sm font-medium text-gray-500 dark:text-gray-400 capitalize">{$page.data.user?.role || t('common.user', currentLocale).toLowerCase()}</div>
            </div>
          </div>
          <div class="mt-3 px-2 space-y-1">
            <a href="/settings" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white">{t('common.settings', currentLocale)}</a>

            {#if $page.data.user?.role === 'developer'}
              <a href="/docs/components" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white">Component Docs</a>
            {/if}

            <div class="px-3 py-2 border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
              <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">{t('language.select', currentLocale)}</p>
              <div class="flex space-x-2">
                <button
                  onclick={() => locale.set('en')}
                  class="bg-blue-600 text-white px-3 py-1 rounded-md flex items-center {currentLocale === 'en' ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}"
                  disabled={currentLocale === 'en'}
                >
                  <span class="mr-1">🇬🇧</span> {t('language.english', currentLocale)}
                </button>
                <button
                  onclick={() => locale.set('lt')}
                  class="bg-blue-600 text-white px-3 py-1 rounded-md flex items-center {currentLocale === 'lt' ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}"
                  disabled={currentLocale === 'lt'}
                >
                  <span class="mr-1">🇱🇹</span> {t('language.lithuanian', currentLocale)}
                </button>
              </div>
            </div>

            <!-- Theme selection -->
            <div class="px-3 py-2 border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
              <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">{t('theme.select', currentLocale)}</p>
              <div class="flex flex-col space-y-2">
                <button
                  onclick={() => theme.set('light')}
                  class="bg-blue-600 text-white px-3 py-1 rounded-md flex items-center w-full justify-start {currentTheme === 'light' ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}"
                  disabled={currentTheme === 'light'}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
                  </svg>
                  {t('theme.light', currentLocale)}
                </button>
                <button
                  onclick={() => theme.set('dark')}
                  class="bg-blue-600 text-white px-3 py-1 rounded-md flex items-center w-full justify-start {currentTheme === 'dark' ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'}"
                  disabled={currentTheme === 'dark'}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                  </svg>
                  {t('theme.dark', currentLocale)}
                </button>
              </div>
            </div>

            <form action="/auth/logout" method="POST" class="mt-3" use:enhance>
              <Button
                type="submit"
                variant="danger"
                size="md"
                class="w-full justify-center py-2 mt-2"
              >
                <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span class="font-medium">{t('common.signOut', currentLocale)}</span>
              </Button>
            </form>
          </div>
        </div>
      </div>
    {/if}
  </header>
{/if}
