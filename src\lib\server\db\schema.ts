import { pgTable, text, integer, timestamp, boolean, pgEnum, time } from 'drizzle-orm/pg-core';

export const roleEnum = pgEnum('role', ['student', 'lecturer', 'admin', 'developer']);

export const organization = pgTable('organization', {
	id: text('id').primaryKey(),
	name: text('name').notNull().unique(),
	description: text('description'),
	isActive: boolean('is_active').notNull().default(true),
	isFrozen: boolean('is_frozen').notNull().default(false),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(),
	createdBy: text('created_by')
});

export const user = pgTable('user', {
	id: text('id').primaryKey(),
	username: text('username').notNull().unique(),
	name: text('name'),
	passwordHash: text('password_hash').notNull(),
	email: text('email').notNull().unique(),
	role: roleEnum('role').notNull().default('student'),
	isActive: boolean('is_active').notNull().default(true),
	isApproved: boolean('is_approved').notNull().default(false),
	organization: text('organization').references(() => organization.id),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const session = pgTable('session', {
	id: text('id').primaryKey(),
	userId: text('user_id').notNull().references(() => user.id),
	expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull()
});

export const project = pgTable('project', {
	id: text('id').primaryKey(),
	name: text('name').notNull(),
	description: text('description'),
	maxAttempts: integer('max_attempts').default(0),
	deadline: timestamp('deadline', { withTimezone: true, mode: 'date' }),
	isHidden: boolean('is_hidden').default(false).notNull(),
	createdBy: text('created_by').notNull().references(() => user.id),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(),
	updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const submission = pgTable('submission', {
	id: text('id').primaryKey(),
	projectId: text('project_id').notNull().references(() => project.id),
	studentId: text('student_id').notNull().references(() => user.id),
	filePath: text('file_path').notNull(),
	fileSize: integer('file_size').notNull(),
	originalFilename: text('original_filename').notNull(),
	csFileContent: text('cs_file_content'),
	csFileName: text('cs_file_name'),
	hasComplexityAnalysis: boolean('has_complexity_analysis').default(false).notNull(),
	submittedAt: timestamp('submitted_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const submissionAttempt = pgTable('submission_attempt', {
	id: text('id').primaryKey(),
	projectId: text('project_id').notNull().references(() => project.id),
	studentId: text('student_id').notNull().references(() => user.id),
	submissionId: text('submission_id').references(() => submission.id),
	attemptedAt: timestamp('attempted_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const csFile = pgTable('cs_file', {
	id: text('id').primaryKey(),
	submissionId: text('submission_id').notNull().references(() => submission.id),
	fileName: text('file_name').notNull(),
	filePath: text('file_path'),
	fileContent: text('file_content').notNull(),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const group = pgTable('group', {
	id: text('id').primaryKey(),
	name: text('name').notNull(),
	description: text('description'),
	createdBy: text('created_by').notNull().references(() => user.id),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(),
	updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(),
	joinEnabled: boolean('join_enabled').default(false).notNull(),
	joinCode: text('join_code'),
	joinCodeExpiry: timestamp('join_code_expiry', { withTimezone: true, mode: 'date' })
});

export const groupMember = pgTable('group_member', {
	id: text('id').primaryKey(),
	groupId: text('group_id').notNull().references(() => group.id),
	studentId: text('student_id').notNull().references(() => user.id),
	addedBy: text('added_by').notNull().references(() => user.id),
	addedAt: timestamp('added_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const groupManager = pgTable('group_manager', {
	id: text('id').primaryKey(),
	groupId: text('group_id').notNull().references(() => group.id),
	lecturerId: text('lecturer_id').notNull().references(() => user.id),
	addedBy: text('added_by').notNull().references(() => user.id),
	addedAt: timestamp('added_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const weekdayEnum = pgEnum('weekday', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']);

export const lectureTime = pgTable('lecture_time', {
	id: text('id').primaryKey(),
	groupId: text('group_id').notNull().references(() => group.id),
	weekday: weekdayEnum('weekday').notNull(),
	startTime: time('start_time').notNull(),
	endTime: time('end_time').notNull(),
	location: text('location'),
	createdBy: text('created_by').notNull().references(() => user.id),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(),
	updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const projectGroup = pgTable('project_group', {
	id: text('id').primaryKey(),
	projectId: text('project_id').notNull().references(() => project.id),
	groupId: text('group_id').notNull().references(() => group.id),
	createdBy: text('created_by').notNull().references(() => user.id),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const projectStudent = pgTable('project_student', {
	id: text('id').primaryKey(),
	projectId: text('project_id').notNull().references(() => project.id),
	studentId: text('student_id').notNull().references(() => user.id),
	createdBy: text('created_by').notNull().references(() => user.id),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const passwordReset = pgTable('password_reset', {
	id: text('id').primaryKey(),
	userId: text('user_id').notNull().references(() => user.id),
	token: text('token').notNull().unique(),
	expiresAt: text('expires_at').notNull(),
	isUsed: boolean('is_used').notNull().default(false),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const complexityAnalysis = pgTable('complexity_analysis', {
	id: text('id').primaryKey(),
	submissionId: text('submission_id').notNull().references(() => submission.id),
	tables: text('tables').notNull(),
	includedPages: text('included_pages').notNull(),
	excludedPages: text('excluded_pages'),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull()
});

export const batchFile = pgTable('batch_file', {
	id: text('id').primaryKey(),
	fileName: text('file_name').notNull(),
	filePath: text('file_path'),
	fileContent: text('file_content').notNull(),
	batchId: text('batch_id').notNull(),
	createdAt: timestamp('created_at', { withTimezone: true, mode: 'date' }).defaultNow().notNull(),
	createdBy: text('created_by').references(() => user.id)
});

export type User = typeof user.$inferSelect;
export type Session = typeof session.$inferSelect;
export type Project = typeof project.$inferSelect;
export type Submission = typeof submission.$inferSelect;
export type SubmissionAttempt = typeof submissionAttempt.$inferSelect;
export type CSFile = typeof csFile.$inferSelect;
export type Organization = typeof organization.$inferSelect;
export type Group = typeof group.$inferSelect;
export type GroupMember = typeof groupMember.$inferSelect;
export type GroupManager = typeof groupManager.$inferSelect;
export type LectureTime = typeof lectureTime.$inferSelect;
export type ProjectGroup = typeof projectGroup.$inferSelect;
export type ProjectStudent = typeof projectStudent.$inferSelect;
export type PasswordReset = typeof passwordReset.$inferSelect;
export type ComplexityAnalysis = typeof complexityAnalysis.$inferSelect;
export type BatchFile = typeof batchFile.$inferSelect;